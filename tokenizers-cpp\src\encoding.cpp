#include "tokenizers/encoding.hpp"
#include <algorithm>

namespace tokenizers {

Encoding Encoding::from_tokens(const std::vector<Token>& tokens, uint32_t type_id) {
    const size_t length = tokens.size();
    
    std::vector<uint32_t> ids;
    std::vector<std::string> token_strings;
    std::vector<std::pair<size_t, size_t>> offsets;
    
    ids.reserve(length);
    token_strings.reserve(length);
    offsets.reserve(length);
    
    for (const auto& token : tokens) {
        ids.push_back(token.id);
        token_strings.push_back(token.value);
        offsets.push_back(token.offsets);
    }
    
    return Encoding(
        std::move(ids),
        std::vector<uint32_t>(length, type_id),  // type_ids
        std::move(token_strings),                // tokens
        std::vector<std::optional<uint32_t>>(length, std::nullopt),  // words
        std::move(offsets),                      // offsets
        std::vector<uint32_t>(length, 0),       // special_tokens_mask
        std::vector<uint32_t>(length, 1)        // attention_mask
    );
}

size_t Encoding::n_sequences() const {
    if (sequence_ranges_.empty()) {
        return ids_.empty() ? 0 : 1;
    }
    return sequence_ranges_.size();
}

void Encoding::clear() {
    ids_.clear();
    type_ids_.clear();
    tokens_.clear();
    words_.clear();
    offsets_.clear();
    special_tokens_mask_.clear();
    attention_mask_.clear();
    overflowing_.clear();
    sequence_ranges_.clear();
}

void Encoding::pad(size_t length, 
                   uint32_t pad_id, 
                   uint32_t pad_type_id, 
                   const std::string& pad_token, 
                   const std::string& direction) {
    if (ids_.size() >= length) {
        return;  // Already at or above target length
    }
    
    const size_t pad_length = length - ids_.size();
    
    if (direction == "right") {
        // Pad on the right
        ids_.insert(ids_.end(), pad_length, pad_id);
        type_ids_.insert(type_ids_.end(), pad_length, pad_type_id);
        tokens_.insert(tokens_.end(), pad_length, pad_token);
        words_.insert(words_.end(), pad_length, std::nullopt);
        offsets_.insert(offsets_.end(), pad_length, std::make_pair(0, 0));
        special_tokens_mask_.insert(special_tokens_mask_.end(), pad_length, 1);
        attention_mask_.insert(attention_mask_.end(), pad_length, 0);
    } else {
        // Pad on the left
        ids_.insert(ids_.begin(), pad_length, pad_id);
        type_ids_.insert(type_ids_.begin(), pad_length, pad_type_id);
        tokens_.insert(tokens_.begin(), pad_length, pad_token);
        words_.insert(words_.begin(), pad_length, std::nullopt);
        offsets_.insert(offsets_.begin(), pad_length, std::make_pair(0, 0));
        special_tokens_mask_.insert(special_tokens_mask_.begin(), pad_length, 1);
        attention_mask_.insert(attention_mask_.begin(), pad_length, 0);
    }
}

void Encoding::truncate(size_t length, size_t stride, const std::string& direction) {
    if (ids_.size() <= length) {
        return;  // Already at or below target length
    }
    
    // Create overflow encoding if stride > 0
    if (stride > 0 && length > stride) {
        const size_t overflow_start = length - stride;
        const size_t overflow_length = ids_.size() - overflow_start;
        
        if (overflow_length > 0) {
            Encoding overflow;
            overflow.ids_.assign(ids_.begin() + overflow_start, ids_.end());
            overflow.type_ids_.assign(type_ids_.begin() + overflow_start, type_ids_.end());
            overflow.tokens_.assign(tokens_.begin() + overflow_start, tokens_.end());
            overflow.words_.assign(words_.begin() + overflow_start, words_.end());
            overflow.offsets_.assign(offsets_.begin() + overflow_start, offsets_.end());
            overflow.special_tokens_mask_.assign(special_tokens_mask_.begin() + overflow_start, special_tokens_mask_.end());
            overflow.attention_mask_.assign(attention_mask_.begin() + overflow_start, attention_mask_.end());
            
            overflowing_.push_back(std::move(overflow));
        }
    }
    
    if (direction == "right") {
        // Truncate from the right
        ids_.resize(length);
        type_ids_.resize(length);
        tokens_.resize(length);
        words_.resize(length);
        offsets_.resize(length);
        special_tokens_mask_.resize(length);
        attention_mask_.resize(length);
    } else {
        // Truncate from the left
        const size_t remove_count = ids_.size() - length;
        ids_.erase(ids_.begin(), ids_.begin() + remove_count);
        type_ids_.erase(type_ids_.begin(), type_ids_.begin() + remove_count);
        tokens_.erase(tokens_.begin(), tokens_.begin() + remove_count);
        words_.erase(words_.begin(), words_.begin() + remove_count);
        offsets_.erase(offsets_.begin(), offsets_.begin() + remove_count);
        special_tokens_mask_.erase(special_tokens_mask_.begin(), special_tokens_mask_.begin() + remove_count);
        attention_mask_.erase(attention_mask_.begin(), attention_mask_.begin() + remove_count);
    }
}

std::optional<size_t> Encoding::char_to_token(size_t char_pos, size_t sequence_id) const {
    // Find the sequence range
    std::pair<size_t, size_t> range;
    if (sequence_ranges_.empty()) {
        if (sequence_id > 0) return std::nullopt;
        range = {0, ids_.size()};
    } else {
        auto it = sequence_ranges_.find(sequence_id);
        if (it == sequence_ranges_.end()) return std::nullopt;
        range = it->second;
    }
    
    // Search for the token containing this character position
    for (size_t i = range.first; i < range.second; ++i) {
        if (i < offsets_.size()) {
            const auto& offset = offsets_[i];
            if (char_pos >= offset.first && char_pos < offset.second) {
                return i;
            }
        }
    }
    
    return std::nullopt;
}

std::optional<uint32_t> Encoding::char_to_word(size_t char_pos, size_t sequence_id) const {
    auto token_idx = char_to_token(char_pos, sequence_id);
    if (!token_idx || *token_idx >= words_.size()) {
        return std::nullopt;
    }
    return words_[*token_idx];
}

std::optional<std::pair<size_t, size_t>> Encoding::token_to_chars(size_t token_index, size_t sequence_id) const {
    // Find the sequence range
    std::pair<size_t, size_t> range;
    if (sequence_ranges_.empty()) {
        if (sequence_id > 0) return std::nullopt;
        range = {0, ids_.size()};
    } else {
        auto it = sequence_ranges_.find(sequence_id);
        if (it == sequence_ranges_.end()) return std::nullopt;
        range = it->second;
    }
    
    if (token_index < range.first || token_index >= range.second || token_index >= offsets_.size()) {
        return std::nullopt;
    }
    
    return offsets_[token_index];
}

std::optional<std::pair<size_t, size_t>> Encoding::word_to_chars(uint32_t word_index, size_t sequence_id) const {
    // Find the sequence range
    std::pair<size_t, size_t> range;
    if (sequence_ranges_.empty()) {
        if (sequence_id > 0) return std::nullopt;
        range = {0, ids_.size()};
    } else {
        auto it = sequence_ranges_.find(sequence_id);
        if (it == sequence_ranges_.end()) return std::nullopt;
        range = it->second;
    }
    
    size_t start_char = SIZE_MAX;
    size_t end_char = 0;
    bool found = false;
    
    for (size_t i = range.first; i < range.second && i < words_.size(); ++i) {
        if (words_[i] && *words_[i] == word_index) {
            found = true;
            if (i < offsets_.size()) {
                start_char = std::min(start_char, offsets_[i].first);
                end_char = std::max(end_char, offsets_[i].second);
            }
        }
    }
    
    return found ? std::make_optional(std::make_pair(start_char, end_char)) : std::nullopt;
}

std::optional<std::pair<size_t, size_t>> Encoding::word_to_tokens(uint32_t word_index, size_t sequence_id) const {
    // Find the sequence range
    std::pair<size_t, size_t> range;
    if (sequence_ranges_.empty()) {
        if (sequence_id > 0) return std::nullopt;
        range = {0, ids_.size()};
    } else {
        auto it = sequence_ranges_.find(sequence_id);
        if (it == sequence_ranges_.end()) return std::nullopt;
        range = it->second;
    }
    
    size_t start_token = SIZE_MAX;
    size_t end_token = 0;
    bool found = false;
    
    for (size_t i = range.first; i < range.second && i < words_.size(); ++i) {
        if (words_[i] && *words_[i] == word_index) {
            found = true;
            start_token = std::min(start_token, i);
            end_token = std::max(end_token, i + 1);
        }
    }
    
    return found ? std::make_optional(std::make_pair(start_token, end_token)) : std::nullopt;
}

} // namespace tokenizers
