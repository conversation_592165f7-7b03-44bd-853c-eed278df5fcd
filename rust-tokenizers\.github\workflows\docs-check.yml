name: Documentation

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12

      - name: Install dependencies
        run: pip install sphinx sphinx_rtd_theme setuptools-rust

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Build tokenizers
        working-directory: ./bindings/python
        run: pip install -e .

      - name: Build documentation
        working-directory: ./docs
        run: make clean && make html_all O="-W --keep-going"

      - name: Upload built doc
        uses: actions/upload-artifact@v4
        with:
          name: documentation
          path: ./docs/build/*
