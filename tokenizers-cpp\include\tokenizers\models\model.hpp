#pragma once

#include "../token.hpp"
#include <vector>
#include <string>
#include <unordered_map>
#include <optional>
#include <memory>
#include <cstdint>

namespace tokenizers {

// Forward declarations
namespace trainers {
    class Trainer;
}

namespace models {

/**
 * @brief Base class for all tokenization models
 * 
 * A Model represents the core tokenization algorithm (like BPE, WordPiece, etc.).
 * It is responsible for:
 * - Converting text sequences into tokens
 * - Managing the vocabulary mapping
 * - Providing token-to-ID and ID-to-token conversions
 */
class Model {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~Model() = default;
    
    /**
     * @brief Tokenize the given sequence into multiple underlying tokens
     * 
     * The offsets on the tokens are expected to be relative to the given sequence.
     * 
     * @param sequence The input text sequence to tokenize
     * @return std::vector<Token> Vector of tokens with IDs, values, and offsets
     */
    virtual std::vector<Token> tokenize(const std::string& sequence) = 0;
    
    /**
     * @brief Find the ID associated with a string token
     * 
     * @param token The token string to look up
     * @return std::optional<uint32_t> The token ID if found, nullopt otherwise
     */
    virtual std::optional<uint32_t> token_to_id(const std::string& token) const = 0;
    
    /**
     * @brief Find the string token associated with an ID
     * 
     * @param id The token ID to look up
     * @return std::optional<std::string> The token string if found, nullopt otherwise
     */
    virtual std::optional<std::string> id_to_token(uint32_t id) const = 0;
    
    /**
     * @brief Retrieve the entire vocabulary mapping (token -> ID)
     * 
     * @return std::unordered_map<std::string, uint32_t> The vocabulary map
     */
    virtual std::unordered_map<std::string, uint32_t> get_vocab() const = 0;
    
    /**
     * @brief Retrieve the size of the vocabulary
     * 
     * @return size_t The vocabulary size
     */
    virtual size_t get_vocab_size() const = 0;
    
    /**
     * @brief Get a trainer for this model
     *
     * @return std::unique_ptr<trainers::Trainer> A trainer instance for this model type
     */
    virtual std::unique_ptr<trainers::Trainer> get_trainer() = 0;
    
    /**
     * @brief Save the model to a file
     * 
     * @param path The file path to save to
     * @param pretty Whether to format the output nicely
     */
    virtual void save(const std::string& path, bool pretty = false) const = 0;
    
    /**
     * @brief Clone this model
     * 
     * @return std::unique_ptr<Model> A copy of this model
     */
    virtual std::unique_ptr<Model> clone() const = 0;
    
    /**
     * @brief Get the model type name
     * 
     * @return std::string The model type (e.g., "BPE", "WordPiece")
     */
    virtual std::string get_type() const = 0;
};

} // namespace models
} // namespace tokenizers
