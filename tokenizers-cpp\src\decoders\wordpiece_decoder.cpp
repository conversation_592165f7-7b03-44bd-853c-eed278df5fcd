#include "tokenizers/decoders/wordpiece_decoder.hpp"
#include <sstream>
#include <regex>

namespace tokenizers {
namespace decoders {

WordPieceDecoder::WordPieceDecoder(const std::string& prefix, bool cleanup)
    : prefix_(prefix), cleanup_(cleanup) {
}

std::string WordPieceDecoder::decode(const std::vector<std::string>& tokens) {
    if (tokens.empty()) {
        return "";
    }
    
    std::ostringstream result;
    
    for (size_t i = 0; i < tokens.size(); ++i) {
        std::string token = tokens[i];
        
        // Check if this token has the continuation prefix
        bool is_continuation = false;
        if (token.length() >= prefix_.length() && 
            token.substr(0, prefix_.length()) == prefix_) {
            is_continuation = true;
            // Remove the prefix
            token = token.substr(prefix_.length());
        }
        
        // Add space before token if it's not a continuation and not the first token
        if (i > 0 && !is_continuation) {
            result << " ";
        }
        
        // Add the token
        result << token;
    }
    
    std::string decoded = result.str();
    
    // Clean up extra spaces if requested
    if (cleanup_) {
        decoded = cleanup_text(decoded);
    }
    
    return decoded;
}

std::unique_ptr<Decoder> WordPieceDecoder::clone() const {
    return std::make_unique<WordPieceDecoder>(prefix_, cleanup_);
}

std::string WordPieceDecoder::cleanup_text(const std::string& text) const {
    if (text.empty()) {
        return text;
    }
    
    // Simple cleanup: remove multiple consecutive spaces and trim
    std::string result = text;
    
    // Replace multiple spaces with single space
    std::regex multiple_spaces("\\s+");
    result = std::regex_replace(result, multiple_spaces, " ");
    
    // Trim leading and trailing spaces
    size_t start = result.find_first_not_of(" \t\n\r");
    if (start == std::string::npos) {
        return "";
    }
    
    size_t end = result.find_last_not_of(" \t\n\r");
    return result.substr(start, end - start + 1);
}

} // namespace decoders
} // namespace tokenizers
