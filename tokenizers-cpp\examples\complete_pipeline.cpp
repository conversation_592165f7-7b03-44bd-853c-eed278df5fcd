#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/models/wordpiece.hpp"
#include "tokenizers/normalizers/bert_normalizer.hpp"
#include "tokenizers/normalizers/strip_normalizer.hpp"
#include "tokenizers/normalizers/sequence_normalizer.hpp"
#include "tokenizers/pre_tokenizers/whitespace.hpp"
#include "tokenizers/pre_tokenizers/bert_pre_tokenizer.hpp"
#include "tokenizers/post_processors/bert_processing.hpp"

using namespace tokenizers;

int main() {
    std::cout << "Complete Tokenization Pipeline Example\n";
    std::cout << "======================================\n\n";
    
    try {
        std::cout << "Building a complete BERT-like tokenizer...\n\n";
        
        // Step 1: Create a WordPiece model with BERT-like vocabulary
        std::cout << "Step 1: Creating <PERSON><PERSON>iece model\n";
        std::cout << "--------------------------------\n";
        
        models::WordPiece::Vocab vocab = {
            // Special tokens
            {"[UNK]", 0}, {"[CLS]", 1}, {"[SEP]", 2}, {"[PAD]", 3}, {"[MASK]", 4},
            
            // Single characters
            {"a", 5}, {"b", 6}, {"c", 7}, {"d", 8}, {"e", 9}, {"f", 10}, {"g", 11}, {"h", 12},
            {"i", 13}, {"j", 14}, {"k", 15}, {"l", 16}, {"m", 17}, {"n", 18}, {"o", 19}, {"p", 20},
            {"q", 21}, {"r", 22}, {"s", 23}, {"t", 24}, {"u", 25}, {"v", 26}, {"w", 27}, {"x", 28},
            {"y", 29}, {"z", 30}, {"!", 31}, {".", 32}, {",", 33}, {"?", 34}, {"'", 35}, {":", 36},
            
            // Common subwords with ## prefix
            {"##ing", 40}, {"##ed", 41}, {"##er", 42}, {"##est", 43}, {"##ly", 44}, {"##tion", 45},
            {"##al", 46}, {"##ness", 47}, {"##ment", 48}, {"##able", 49}, {"##ful", 50},
            {"##ize", 51}, {"##izer", 52}, {"##ization", 53}, {"##s", 54}, {"##n", 55}, {"##t", 56},
            
            // Common words
            {"the", 60}, {"and", 61}, {"is", 62}, {"in", 63}, {"to", 64}, {"of", 65}, {"a", 66},
            {"that", 67}, {"it", 68}, {"with", 69}, {"for", 70}, {"as", 71}, {"was", 72}, {"on", 73},
            {"are", 74}, {"you", 75}, {"this", 76}, {"be", 77}, {"at", 78}, {"have", 79}, {"or", 80},
            
            // Example words
            {"hello", 90}, {"world", 91}, {"test", 92}, {"example", 93}, {"word", 94}, {"piece", 95},
            {"token", 96}, {"bert", 97}, {"model", 98}, {"natural", 99}, {"language", 100},
            {"processing", 101}, {"machine", 102}, {"learning", 103}, {"artificial", 104},
            {"intelligence", 105}, {"computer", 106}, {"science", 107}
        };
        
        auto wordpiece_model = std::make_unique<models::WordPiece>(vocab, "[UNK]", "##", 100);
        std::cout << "Created WordPiece model with " << wordpiece_model->get_vocab_size() << " tokens\n\n";
        
        // Step 2: Create tokenizer with the model
        std::cout << "Step 2: Creating tokenizer\n";
        std::cout << "-------------------------\n";
        
        Tokenizer tokenizer(std::move(wordpiece_model));
        std::cout << "Tokenizer created\n\n";
        
        // Step 3: Add normalizer (BERT-style normalization)
        std::cout << "Step 3: Adding normalizer\n";
        std::cout << "------------------------\n";
        
        auto normalizer = normalizers::SequenceNormalizer::builder()
            .add(std::make_unique<normalizers::StripNormalizer>(true, true))
            .add(std::make_unique<normalizers::BertNormalizer>(true, false, true, true))
            .build();
        
        tokenizer.set_normalizer(std::make_unique<normalizers::SequenceNormalizer>(std::move(normalizer)));
        std::cout << "Added BERT-style normalizer (strip + clean + strip accents + lowercase)\n\n";
        
        // Step 4: Add pre-tokenizer (BERT-style pre-tokenization)
        std::cout << "Step 4: Adding pre-tokenizer\n";
        std::cout << "----------------------------\n";
        
        tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::BertPreTokenizer>());
        std::cout << "Added BERT-style pre-tokenizer (whitespace + punctuation splitting)\n\n";
        
        // Step 5: Add post-processor (BERT-style post-processing)
        std::cout << "Step 5: Adding post-processor\n";
        std::cout << "-----------------------------\n";
        
        auto post_processor = processors::BertProcessing::builder()
            .sep_token(AddedToken::from("[SEP]", true))
            .cls_token(AddedToken::from("[CLS]", true))
            .build();

        tokenizer.set_post_processor(std::make_unique<processors::BertProcessing>(std::move(post_processor)));
        std::cout << "Added BERT-style post-processor ([CLS] and [SEP] tokens)\n\n";
        
        // Step 6: Add special tokens
        std::cout << "Step 6: Adding special tokens\n";
        std::cout << "-----------------------------\n";
        
        std::vector<AddedToken> special_tokens = {
            AddedToken::from("[UNK]", true),
            AddedToken::from("[CLS]", true),
            AddedToken::from("[SEP]", true),
            AddedToken::from("[PAD]", true),
            AddedToken::from("[MASK]", true)
        };
        
        auto added_count = tokenizer.add_special_tokens(special_tokens);
        std::cout << "Added " << added_count << " special tokens\n\n";
        
        // Step 7: Test the complete pipeline
        std::cout << "Step 7: Testing the complete pipeline\n";
        std::cout << "=====================================\n\n";
        
        std::vector<std::string> test_sentences = {
            "Hello, World! This is a test.",
            "Natural Language Processing with BERT",
            "Machine Learning and Artificial Intelligence",
            "  UPPERCASE text with   EXTRA spaces  ",
            "Contractions like don't and won't",
            "Numbers 123 and symbols @#$%"
        };
        
        for (const auto& sentence : test_sentences) {
            std::cout << "Input: \"" << sentence << "\"\n";
            
            auto encoding = tokenizer.encode(sentence);
            
            const auto& tokens = encoding.get_tokens();
            const auto& ids = encoding.get_ids();
            const auto& type_ids = encoding.get_type_ids();
            const auto& attention_mask = encoding.get_attention_mask();
            const auto& special_tokens_mask = encoding.get_special_tokens_mask();
            
            std::cout << "Tokens (" << tokens.size() << "): [";
            for (size_t i = 0; i < tokens.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << tokens[i] << "\"";
            }
            std::cout << "]\n";
            
            std::cout << "IDs: [";
            for (size_t i = 0; i < ids.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << ids[i];
            }
            std::cout << "]\n";
            
            if (!type_ids.empty()) {
                std::cout << "Type IDs: [";
                for (size_t i = 0; i < type_ids.size(); ++i) {
                    if (i > 0) std::cout << ", ";
                    std::cout << type_ids[i];
                }
                std::cout << "]\n";
            }
            
            std::cout << "Attention Mask: [";
            for (size_t i = 0; i < attention_mask.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << attention_mask[i];
            }
            std::cout << "]\n";
            
            std::cout << "Special Tokens Mask: [";
            for (size_t i = 0; i < special_tokens_mask.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << special_tokens_mask[i];
            }
            std::cout << "]\n";
            
            // Test decoding
            auto decoded = tokenizer.decode(ids);
            std::cout << "Decoded: \"" << decoded << "\"\n\n";
        }
        
        // Step 8: Test batch processing
        std::cout << "Step 8: Testing batch processing\n";
        std::cout << "================================\n\n";
        
        std::vector<std::string> batch_sentences = {
            "First sentence for batch processing.",
            "Second sentence with different length.",
            "Short sentence."
        };
        
        auto batch_encodings = tokenizer.encode_batch(batch_sentences);
        
        std::cout << "Batch processing results:\n";
        for (size_t i = 0; i < batch_encodings.size(); ++i) {
            std::cout << "Sentence " << (i + 1) << ": " << batch_encodings[i].get_tokens().size() << " tokens\n";
        }
        std::cout << "\n";
        
        // Step 9: Summary
        std::cout << "Step 9: Pipeline Summary\n";
        std::cout << "=======================\n\n";
        
        std::cout << "Complete BERT-like tokenization pipeline created with:\n";
        std::cout << "✓ WordPiece model with " << tokenizer.get_vocab_size() << " vocabulary entries\n";
        std::cout << "✓ BERT-style normalization (strip + clean + strip accents + lowercase)\n";
        std::cout << "✓ BERT-style pre-tokenization (whitespace + punctuation splitting)\n";
        std::cout << "✓ BERT-style post-processing ([CLS] and [SEP] tokens)\n";
        std::cout << "✓ Special tokens support\n";
        std::cout << "✓ Batch processing capability\n";
        std::cout << "✓ Encoding/decoding functionality\n\n";
        
        std::cout << "The tokenizer is ready for use in NLP applications!\n";
        std::cout << "Complete pipeline example finished successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
