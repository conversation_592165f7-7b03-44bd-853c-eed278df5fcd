#include <gtest/gtest.h>
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/trainers/bpe_trainer.hpp"
#include <fstream>
#include <sstream>

using namespace tokenizers;

class BPETest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a simple vocabulary and merges for testing
        vocab_ = {
            // Single characters
            {"h", 0}, {"e", 1}, {"l", 2}, {"o", 3}, {" ", 4}, {"w", 5}, {"r", 6}, {"d", 7},
            {"!", 8}, {"t", 9}, {"s", 10},
            
            // Merged tokens
            {"he", 11}, {"ll", 12}, {"lo", 13}, {"wo", 14}, {"or", 15}, {"rl", 16}, {"ld", 17},
            {"hello", 18}, {"world", 19}
        };
        
        merges_ = {
            {"h", "e"},      // he
            {"l", "l"},      // ll
            {"l", "o"},      // lo
            {"w", "o"},      // wo
            {"o", "r"},      // or
            {"r", "l"},      // rl
            {"l", "d"},      // ld
            {"he", "ll"},    // hell (not in vocab, so won't be used)
            {"hello", " "},  // "hello " (not in vocab)
        };
        
        bpe_ = std::make_unique<models::BPE>(vocab_, merges_);
    }

    models::BPE::Vocab vocab_;
    models::BPE::Merges merges_;
    std::unique_ptr<models::BPE> bpe_;
};

TEST_F(BPETest, BasicVocabularyAccess) {
    EXPECT_EQ(bpe_->get_vocab_size(), vocab_.size());
    
    auto vocab = bpe_->get_vocab();
    EXPECT_EQ(vocab.size(), vocab_.size());
    
    // Check some specific mappings
    EXPECT_EQ(bpe_->token_to_id("h"), 0);
    EXPECT_EQ(bpe_->token_to_id("hello"), 18);
    EXPECT_FALSE(bpe_->token_to_id("nonexistent").has_value());
    
    EXPECT_EQ(bpe_->id_to_token(0), "h");
    EXPECT_EQ(bpe_->id_to_token(18), "hello");
    EXPECT_FALSE(bpe_->id_to_token(999).has_value());
}

TEST_F(BPETest, BasicTokenization) {
    auto tokens = bpe_->tokenize("hello");
    
    EXPECT_FALSE(tokens.empty());
    
    // Check that all tokens have valid IDs
    for (const auto& token : tokens) {
        EXPECT_TRUE(bpe_->token_to_id(token.value).has_value());
        EXPECT_EQ(bpe_->token_to_id(token.value).value(), token.id);
    }
    
    // Check offsets are reasonable
    size_t last_end = 0;
    for (const auto& token : tokens) {
        EXPECT_GE(token.offsets.first, last_end);
        EXPECT_GT(token.offsets.second, token.offsets.first);
        last_end = token.offsets.second;
    }
}

TEST_F(BPETest, EmptyStringTokenization) {
    auto tokens = bpe_->tokenize("");
    EXPECT_TRUE(tokens.empty());
}

TEST_F(BPETest, SingleCharacterTokenization) {
    auto tokens = bpe_->tokenize("h");
    
    EXPECT_EQ(tokens.size(), 1);
    EXPECT_EQ(tokens[0].value, "h");
    EXPECT_EQ(tokens[0].id, 0);
    EXPECT_EQ(tokens[0].offsets, std::make_pair(size_t(0), size_t(1)));
}

TEST_F(BPETest, MultipleWordsTokenization) {
    auto tokens = bpe_->tokenize("hello world");
    
    EXPECT_FALSE(tokens.empty());
    
    // Should have tokens for both "hello" and "world"
    bool found_hello_related = false;
    bool found_world_related = false;
    
    for (const auto& token : tokens) {
        if (token.value.find("h") != std::string::npos || 
            token.value.find("e") != std::string::npos ||
            token.value == "hello") {
            found_hello_related = true;
        }
        if (token.value.find("w") != std::string::npos || 
            token.value.find("d") != std::string::npos ||
            token.value == "world") {
            found_world_related = true;
        }
    }
    
    EXPECT_TRUE(found_hello_related);
    EXPECT_TRUE(found_world_related);
}

TEST_F(BPETest, ModelType) {
    EXPECT_EQ(bpe_->get_type(), "BPE");
}

TEST_F(BPETest, Clone) {
    auto cloned = bpe_->clone();
    
    EXPECT_NE(cloned.get(), nullptr);
    EXPECT_EQ(cloned->get_type(), "BPE");
    EXPECT_EQ(cloned->get_vocab_size(), bpe_->get_vocab_size());
    
    // Check that vocabularies are the same
    auto original_vocab = bpe_->get_vocab();
    auto cloned_vocab = cloned->get_vocab();
    
    EXPECT_EQ(original_vocab.size(), cloned_vocab.size());
    
    for (const auto& [token, id] : original_vocab) {
        EXPECT_EQ(cloned_vocab[token], id);
    }
}

TEST_F(BPETest, Builder) {
    auto built_bpe = models::BPE::builder()
        .vocab(vocab_)
        .merges(merges_)
        .unk_token("<unk>")
        .dropout(0.1f)
        .build();
    
    EXPECT_EQ(built_bpe.get_vocab_size(), vocab_.size());
    EXPECT_EQ(built_bpe.get_unk_token(), "<unk>");
    EXPECT_EQ(built_bpe.get_dropout(), 0.1f);
}

TEST_F(BPETest, BuilderMissingVocab) {
    EXPECT_THROW({
        models::BPE::builder()
            .merges(merges_)
            .build();
    }, std::runtime_error);
}

TEST_F(BPETest, BuilderMissingMerges) {
    EXPECT_THROW({
        models::BPE::builder()
            .vocab(vocab_)
            .build();
    }, std::runtime_error);
}

TEST_F(BPETest, DropoutSetting) {
    bpe_->set_dropout(0.5f);
    EXPECT_EQ(bpe_->get_dropout(), 0.5f);
    
    bpe_->set_dropout(std::nullopt);
    EXPECT_FALSE(bpe_->get_dropout().has_value());
}

TEST_F(BPETest, UnkTokenSetting) {
    bpe_->set_unk_token("<unk>");
    EXPECT_EQ(bpe_->get_unk_token(), "<unk>");
    
    bpe_->set_unk_token(std::nullopt);
    EXPECT_FALSE(bpe_->get_unk_token().has_value());
}

// Test BPE file loading (mock test since we don't have actual files)
TEST(BPEFileTest, FromFileError) {
    // Test with non-existent files
    EXPECT_THROW({
        models::BPE::from_file("nonexistent_vocab.json", "nonexistent_merges.txt");
    }, std::runtime_error);
}

// Test BPE trainer
class BPETrainerTest : public ::testing::Test {
protected:
    void SetUp() override {
        trainer_ = std::make_unique<trainers::BPETrainer>();
    }

    std::unique_ptr<trainers::BPETrainer> trainer_;
};

TEST_F(BPETrainerTest, DefaultSettings) {
    EXPECT_EQ(trainer_->get_vocab_size(), 30000);
    EXPECT_EQ(trainer_->get_min_frequency(), 2);
    EXPECT_TRUE(trainer_->get_show_progress());
    EXPECT_TRUE(trainer_->get_special_tokens().empty());
    EXPECT_EQ(trainer_->get_type(), "BPETrainer");
}

TEST_F(BPETrainerTest, SettingsModification) {
    trainer_->set_vocab_size(1000);
    trainer_->set_min_frequency(5);
    trainer_->set_show_progress(false);
    
    EXPECT_EQ(trainer_->get_vocab_size(), 1000);
    EXPECT_EQ(trainer_->get_min_frequency(), 5);
    EXPECT_FALSE(trainer_->get_show_progress());
}

TEST_F(BPETrainerTest, SpecialTokens) {
    std::vector<AddedToken> special_tokens = {
        AddedToken::from("<unk>", true),
        AddedToken::from("<pad>", true),
        AddedToken::from("<s>", true),
        AddedToken::from("</s>", true)
    };
    
    trainer_->set_special_tokens(special_tokens);
    
    const auto& tokens = trainer_->get_special_tokens();
    EXPECT_EQ(tokens.size(), 4);
    
    EXPECT_EQ(tokens[0].content, "<unk>");
    EXPECT_EQ(tokens[1].content, "<pad>");
    EXPECT_EQ(tokens[2].content, "<s>");
    EXPECT_EQ(tokens[3].content, "</s>");
    
    for (const auto& token : tokens) {
        EXPECT_TRUE(token.special);
    }
}

TEST_F(BPETrainerTest, Builder) {
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(5000)
        .min_frequency(3)
        .show_progress(false)
        .continuing_subword_prefix("##")
        .end_of_word_suffix("</w>")
        .build();
    
    EXPECT_EQ(trainer.get_vocab_size(), 5000);
    EXPECT_EQ(trainer.get_min_frequency(), 3);
    EXPECT_FALSE(trainer.get_show_progress());
    EXPECT_EQ(trainer.get_continuing_subword_prefix(), "##");
    EXPECT_EQ(trainer.get_end_of_word_suffix(), "</w>");
}

TEST_F(BPETrainerTest, Feed) {
    std::vector<std::string> sequences = {
        "hello world",
        "hello there",
        "world peace",
        "hello world again"
    };
    
    // This should not throw
    EXPECT_NO_THROW(trainer_->feed(sequences));
}

TEST_F(BPETrainerTest, Clone) {
    trainer_->set_vocab_size(1000);
    trainer_->set_min_frequency(5);
    
    auto cloned = trainer_->clone();
    
    EXPECT_NE(cloned.get(), nullptr);
    EXPECT_EQ(cloned->get_vocab_size(), 1000);
    EXPECT_EQ(cloned->get_min_frequency(), 5);
    EXPECT_EQ(cloned->get_type(), "BPETrainer");
}

TEST_F(BPETrainerTest, ContinuingSubwordPrefix) {
    trainer_->set_continuing_subword_prefix("##");
    EXPECT_EQ(trainer_->get_continuing_subword_prefix(), "##");
    
    trainer_->set_continuing_subword_prefix(std::nullopt);
    EXPECT_FALSE(trainer_->get_continuing_subword_prefix().has_value());
}

TEST_F(BPETrainerTest, EndOfWordSuffix) {
    trainer_->set_end_of_word_suffix("</w>");
    EXPECT_EQ(trainer_->get_end_of_word_suffix(), "</w>");
    
    trainer_->set_end_of_word_suffix(std::nullopt);
    EXPECT_FALSE(trainer_->get_end_of_word_suffix().has_value());
}
