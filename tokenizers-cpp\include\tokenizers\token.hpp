#pragma once

#include <string>
#include <utility>
#include <cstdint>

namespace tokenizers {

/**
 * @brief Represents a token with its ID, value, and character offsets
 * 
 * A Token contains:
 * - id: The numerical identifier of the token in the vocabulary
 * - value: The string representation of the token
 * - offsets: The start and end character positions in the original text
 */
struct Token {
    /// The numerical ID of the token
    uint32_t id;
    
    /// The string value of the token
    std::string value;
    
    /// The character offsets (start, end) in the original text
    std::pair<size_t, size_t> offsets;
    
    /**
     * @brief Construct a new Token
     * 
     * @param id The token ID
     * @param value The token string value
     * @param offsets The character offsets (start, end)
     */
    Token(uint32_t id, const std::string& value, std::pair<size_t, size_t> offsets)
        : id(id), value(value), offsets(offsets) {}
    
    /**
     * @brief Construct a new Token with move semantics
     * 
     * @param id The token ID
     * @param value The token string value (moved)
     * @param offsets The character offsets (start, end)
     */
    Token(uint32_t id, std::string&& value, std::pair<size_t, size_t> offsets)
        : id(id), value(std::move(value)), offsets(offsets) {}
    
    /// Default constructor
    Token() : id(0), value(""), offsets({0, 0}) {}
    
    /// Copy constructor
    Token(const Token&) = default;
    
    /// Move constructor
    Token(Token&&) = default;
    
    /// Copy assignment
    Token& operator=(const Token&) = default;
    
    /// Move assignment
    Token& operator=(Token&&) = default;
    
    /// Destructor
    ~Token() = default;
    
    /**
     * @brief Check if two tokens are equal
     * 
     * @param other The other token to compare with
     * @return true if tokens are equal, false otherwise
     */
    bool operator==(const Token& other) const {
        return id == other.id && value == other.value && offsets == other.offsets;
    }
    
    /**
     * @brief Check if two tokens are not equal
     * 
     * @param other The other token to compare with
     * @return true if tokens are not equal, false otherwise
     */
    bool operator!=(const Token& other) const {
        return !(*this == other);
    }
    
    /**
     * @brief Get the length of the token in characters
     * 
     * @return size_t The character length
     */
    size_t length() const {
        return offsets.second - offsets.first;
    }
    
    /**
     * @brief Check if the token is empty
     * 
     * @return true if the token value is empty, false otherwise
     */
    bool empty() const {
        return value.empty();
    }
};

} // namespace tokenizers
