#pragma once

#include "model.hpp"
#include "../trainers/trainer.hpp"
#include <unordered_map>
#include <unordered_set>
#include <optional>
#include <string>
#include <vector>
#include <utility>
#include <cstdint>

namespace tokenizers {
namespace models {

/**
 * @brief A Byte Pair Encoding (BPE) model
 * 
 * BPE is a subword tokenization algorithm that iteratively merges the most frequent
 * pairs of characters or character sequences. It's widely used in neural machine
 * translation and language modeling.
 */
class BPE : public Model {
public:
    /// Type alias for vocabulary (token -> ID mapping)
    using Vocab = std::unordered_map<std::string, uint32_t>;
    
    /// Type alias for reverse vocabulary (ID -> token mapping)
    using VocabR = std::unordered_map<uint32_t, std::string>;
    
    /// Type alias for merges (pair -> (rank, new_id) mapping)
    using Merges = std::vector<std::pair<std::string, std::string>>;
    
    /// Type alias for merge map (pair -> rank mapping)
    using MergeMap = std::unordered_map<std::string, uint32_t>;

private:
    /// The vocabulary assigns a number to each token
    Vocab vocab_;
    
    /// Reversed vocabulary, to rebuild sentences
    VocabR vocab_r_;
    
    /// Contains the merges in order of priority
    Merges merges_;
    
    /// Contains the mapping between pairs and their rank
    MergeMap merge_map_;
    
    /// Dropout probability for merges (0.0 = no dropout)
    std::optional<float> dropout_;
    
    /// The unknown token to be used when we encounter an unknown character
    std::optional<std::string> unk_token_;
    
    /// Continuing subword prefix (e.g., "##" for WordPiece-style)
    std::optional<std::string> continuing_subword_prefix_;
    
    /// End of word suffix (e.g., "</w>" for some BPE variants)
    std::optional<std::string> end_of_word_suffix_;
    
    /// Whether to fuse unknown tokens
    bool fuse_unk_;

public:
    /**
     * @brief Default constructor
     */
    BPE();
    
    /**
     * @brief Construct BPE with vocabulary and merges
     * 
     * @param vocab The vocabulary mapping
     * @param merges The merge rules
     * @param dropout Optional dropout probability
     * @param unk_token Optional unknown token
     * @param continuing_subword_prefix Optional continuing subword prefix
     * @param end_of_word_suffix Optional end of word suffix
     * @param fuse_unk Whether to fuse unknown tokens
     */
    BPE(Vocab vocab,
        Merges merges,
        std::optional<float> dropout = std::nullopt,
        std::optional<std::string> unk_token = std::nullopt,
        std::optional<std::string> continuing_subword_prefix = std::nullopt,
        std::optional<std::string> end_of_word_suffix = std::nullopt,
        bool fuse_unk = false);
    
    /**
     * @brief Create BPE from vocabulary and merges files
     * 
     * @param vocab_file Path to vocabulary file (JSON format)
     * @param merges_file Path to merges file (text format)
     * @return BPE The constructed BPE model
     */
    static BPE from_file(const std::string& vocab_file, const std::string& merges_file);
    
    /**
     * @brief Builder class for BPE
     */
    class Builder {
    private:
        std::optional<Vocab> vocab_;
        std::optional<Merges> merges_;
        std::optional<float> dropout_;
        std::optional<std::string> unk_token_;
        std::optional<std::string> continuing_subword_prefix_;
        std::optional<std::string> end_of_word_suffix_;
        bool fuse_unk_ = false;
        
    public:
        Builder() = default;
        
        Builder& vocab(const Vocab& vocab) { vocab_ = vocab; return *this; }
        Builder& merges(const Merges& merges) { merges_ = merges; return *this; }
        Builder& dropout(float dropout) { dropout_ = dropout; return *this; }
        Builder& unk_token(const std::string& unk_token) { unk_token_ = unk_token; return *this; }
        Builder& continuing_subword_prefix(const std::string& prefix) { continuing_subword_prefix_ = prefix; return *this; }
        Builder& end_of_word_suffix(const std::string& suffix) { end_of_word_suffix_ = suffix; return *this; }
        Builder& fuse_unk(bool fuse_unk) { fuse_unk_ = fuse_unk; return *this; }
        
        BPE build();
    };
    
    /**
     * @brief Create a builder for BPE
     * 
     * @return Builder A BPE builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Model interface implementation
    std::vector<Token> tokenize(const std::string& sequence) override;
    std::optional<uint32_t> token_to_id(const std::string& token) const override;
    std::optional<std::string> id_to_token(uint32_t id) const override;
    std::unordered_map<std::string, uint32_t> get_vocab() const override;
    size_t get_vocab_size() const override;
    std::unique_ptr<trainers::Trainer> get_trainer() override;
    void save(const std::string& path, bool pretty = false) const override;
    std::unique_ptr<Model> clone() const override;
    std::string get_type() const override { return "BPE"; }
    
    // BPE-specific methods
    
    /**
     * @brief Get the merges
     * 
     * @return const Merges& The merge rules
     */
    const Merges& get_merges() const { return merges_; }
    
    /**
     * @brief Get the dropout probability
     * 
     * @return std::optional<float> The dropout probability
     */
    std::optional<float> get_dropout() const { return dropout_; }
    
    /**
     * @brief Set the dropout probability
     * 
     * @param dropout The dropout probability
     */
    void set_dropout(std::optional<float> dropout) { dropout_ = dropout; }
    
    /**
     * @brief Get the unknown token
     * 
     * @return std::optional<std::string> The unknown token
     */
    std::optional<std::string> get_unk_token() const { return unk_token_; }
    
    /**
     * @brief Set the unknown token
     * 
     * @param unk_token The unknown token
     */
    void set_unk_token(std::optional<std::string> unk_token) { unk_token_ = unk_token; }

private:
    /**
     * @brief Apply BPE merges to a word
     * 
     * @param word The word to process
     * @return std::vector<std::string> The subword tokens
     */
    std::vector<std::string> merge_word(const std::string& word) const;
    
    /**
     * @brief Convert a word to tokens with proper IDs and offsets
     * 
     * @param word The original word
     * @param subwords The subword tokens
     * @param start_offset Starting character offset
     * @return std::vector<Token> The tokens with IDs and offsets
     */
    std::vector<Token> word_to_tokens(const std::string& word, 
                                    const std::vector<std::string>& subwords,
                                    size_t start_offset) const;
    
    /**
     * @brief Build the merge map from merges vector
     */
    void build_merge_map();
    
    /**
     * @brief Get the rank of a merge pair
     * 
     * @param pair The merge pair as "token1 token2"
     * @return std::optional<uint32_t> The rank if found
     */
    std::optional<uint32_t> get_merge_rank(const std::string& pair) const;
};

} // namespace models
} // namespace tokenizers
