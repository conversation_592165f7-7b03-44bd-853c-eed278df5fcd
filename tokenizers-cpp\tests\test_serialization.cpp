#include <gtest/gtest.h>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/models/wordpiece.hpp"
#include "tokenizers/models/unigram.hpp"
#include "tokenizers/normalizers/bert_normalizer.hpp"
#include "tokenizers/normalizers/strip_normalizer.hpp"
#include "tokenizers/pre_tokenizers/whitespace.hpp"
#include "tokenizers/pre_tokenizers/bert_pre_tokenizer.hpp"
#include "tokenizers/post_processors/bert_processing.hpp"
#include "tokenizers/decoders/bpe_decoder.hpp"
#include "tokenizers/decoders/wordpiece_decoder.hpp"
#include <fstream>
#include <filesystem>

using namespace tokenizers;

class SerializationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup test data
    }
    
    void TearDown() override {
        // Clean up test files
        cleanup_test_files();
    }
    
    void cleanup_test_files() {
        std::filesystem::remove("test_bpe_model.json");
        std::filesystem::remove("test_wordpiece_model.json");
        std::filesystem::remove("test_unigram_model.json");
        std::filesystem::remove("test_tokenizer.json");
    }
    
    // Helper function to create a test BPE model
    std::unique_ptr<models::BPE> create_test_bpe_model() {
        models::BPE::Vocab vocab = {
            {"h", 0}, {"e", 1}, {"l", 2}, {"o", 3}, {" ", 4}, {"w", 5}, {"r", 6}, {"d", 7},
            {"t", 8}, {"s", 9}, {"i", 10}, {"n", 11}, {"g", 12}, {"a", 13}, {"m", 14}, {"p", 15},
            {"he", 16}, {"ll", 17}, {"lo", 18}, {"wo", 19}, {"or", 20}, {"rl", 21}, {"ld", 22},
            {"hello", 23}, {"world", 24}, {"test", 25}
        };
        
        models::BPE::Merges merges = {
            {"h", "e"}, {"l", "l"}, {"l", "o"}, {"w", "o"}, {"o", "r"}, {"r", "l"}, {"l", "d"}
        };
        
        return std::make_unique<models::BPE>(vocab, merges, 0.0, "[UNK]", "", "");
    }
    
    // Helper function to create a test WordPiece model
    std::unique_ptr<models::WordPiece> create_test_wordpiece_model() {
        models::WordPiece::Vocab vocab = {
            {"[UNK]", 0}, {"[CLS]", 1}, {"[SEP]", 2}, {"[PAD]", 3}, {"[MASK]", 4},
            {"h", 5}, {"e", 6}, {"l", 7}, {"o", 8}, {" ", 9}, {"w", 10}, {"r", 11}, {"d", 12},
            {"##e", 13}, {"##l", 14}, {"##o", 15}, {"##r", 16}, {"##d", 17},
            {"hello", 18}, {"world", 19}, {"test", 20}
        };
        
        return std::make_unique<models::WordPiece>(vocab, "[UNK]", "##", 100);
    }
    
    // Helper function to create a test Unigram model
    std::unique_ptr<models::Unigram> create_test_unigram_model() {
        models::Unigram::Vocab vocab = {
            {"<unk>", {0, -10.0}},
            {"hello", {1, -0.1}},
            {"world", {2, -0.2}},
            {"test", {3, -0.3}},
            {"a", {4, -1.0}},
            {"b", {5, -2.0}},
            {"c", {6, -3.0}}
        };
        
        return std::make_unique<models::Unigram>(vocab, "<unk>", -10.0);
    }
};

TEST_F(SerializationTest, BPEModelSerialization) {
    auto model = create_test_bpe_model();
    
    // Test saving
    EXPECT_NO_THROW(model->save("test_bpe_model.json", true));
    
    // Verify file was created
    EXPECT_TRUE(std::filesystem::exists("test_bpe_model.json"));
    
    // Test that file contains expected content
    std::ifstream file("test_bpe_model.json");
    ASSERT_TRUE(file.is_open());
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    
    // Check for expected JSON structure
    EXPECT_NE(content.find("\"type\""), std::string::npos);
    EXPECT_NE(content.find("\"BPE\""), std::string::npos);
    EXPECT_NE(content.find("\"vocab\""), std::string::npos);
    EXPECT_NE(content.find("\"merges\""), std::string::npos);
}

TEST_F(SerializationTest, WordPieceModelSerialization) {
    auto model = create_test_wordpiece_model();
    
    // Test saving
    EXPECT_NO_THROW(model->save("test_wordpiece_model.json", true));
    
    // Verify file was created
    EXPECT_TRUE(std::filesystem::exists("test_wordpiece_model.json"));
    
    // Test that file contains expected content
    std::ifstream file("test_wordpiece_model.json");
    ASSERT_TRUE(file.is_open());
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    
    // Check for expected JSON structure
    EXPECT_NE(content.find("\"type\""), std::string::npos);
    EXPECT_NE(content.find("\"WordPiece\""), std::string::npos);
    EXPECT_NE(content.find("\"vocab\""), std::string::npos);
    EXPECT_NE(content.find("\"unk_token\""), std::string::npos);
    EXPECT_NE(content.find("\"continuing_subword_prefix\""), std::string::npos);
}

TEST_F(SerializationTest, UnigramModelSerialization) {
    auto model = create_test_unigram_model();
    
    // Test saving
    EXPECT_NO_THROW(model->save("test_unigram_model.json", true));
    
    // Verify file was created
    EXPECT_TRUE(std::filesystem::exists("test_unigram_model.json"));
    
    // Test that file contains expected content
    std::ifstream file("test_unigram_model.json");
    ASSERT_TRUE(file.is_open());
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    
    // Check for expected JSON structure
    EXPECT_NE(content.find("\"type\""), std::string::npos);
    EXPECT_NE(content.find("\"Unigram\""), std::string::npos);
    EXPECT_NE(content.find("\"vocab\""), std::string::npos);
    EXPECT_NE(content.find("\"unk_token\""), std::string::npos);
    EXPECT_NE(content.find("\"min_score\""), std::string::npos);
}

TEST_F(SerializationTest, TokenizerSerialization) {
    // Create a complete tokenizer
    auto model = create_test_bpe_model();
    Tokenizer tokenizer(std::move(model));
    
    // Add components
    tokenizer.set_normalizer(std::make_unique<normalizers::BertNormalizer>());
    tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::BertPreTokenizer>());
    tokenizer.set_post_processor(std::make_unique<processors::BertProcessing>());
    tokenizer.set_decoder(std::make_unique<decoders::BPEDecoder>());
    
    // Add special tokens
    std::vector<AddedToken> special_tokens = {
        AddedToken::from("[UNK]", true),
        AddedToken::from("[CLS]", true),
        AddedToken::from("[SEP]", true)
    };
    tokenizer.add_special_tokens(special_tokens);
    
    // Test saving
    EXPECT_NO_THROW(tokenizer.save("test_tokenizer.json", true));
    
    // Verify file was created
    EXPECT_TRUE(std::filesystem::exists("test_tokenizer.json"));
    
    // Test that file contains expected content
    std::ifstream file("test_tokenizer.json");
    ASSERT_TRUE(file.is_open());
    
    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    
    // Check for expected JSON structure
    EXPECT_NE(content.find("\"model\""), std::string::npos);
    EXPECT_NE(content.find("\"normalizer\""), std::string::npos);
    EXPECT_NE(content.find("\"pre_tokenizer\""), std::string::npos);
    EXPECT_NE(content.find("\"post_processor\""), std::string::npos);
    EXPECT_NE(content.find("\"decoder\""), std::string::npos);
    EXPECT_NE(content.find("\"added_tokens\""), std::string::npos);
}

TEST_F(SerializationTest, SerializationRoundTrip) {
    // Create original tokenizer
    auto model = create_test_bpe_model();
    Tokenizer original_tokenizer(std::move(model));
    
    // Add some configuration
    original_tokenizer.set_normalizer(std::make_unique<normalizers::BertNormalizer>());
    original_tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::Whitespace>());
    
    std::vector<AddedToken> special_tokens = {
        AddedToken::from("[UNK]", true),
        AddedToken::from("[PAD]", true)
    };
    original_tokenizer.add_special_tokens(special_tokens);
    
    // Test tokenization before saving
    std::string test_text = "hello world test";
    auto original_encoding = original_tokenizer.encode(test_text);
    
    // Save tokenizer
    original_tokenizer.save("test_tokenizer.json", true);
    
    // Load tokenizer
    auto loaded_tokenizer = Tokenizer::from_file("test_tokenizer.json");
    ASSERT_TRUE(loaded_tokenizer.has_value());
    
    // Test that loaded tokenizer works
    auto loaded_encoding = loaded_tokenizer->encode(test_text);
    
    // Compare results (they should be similar, though exact match depends on implementation)
    EXPECT_FALSE(original_encoding.is_empty());
    EXPECT_FALSE(loaded_encoding.is_empty());
    
    // Check that special tokens are preserved
    EXPECT_TRUE(loaded_tokenizer->token_to_id("[UNK]").has_value());
    EXPECT_TRUE(loaded_tokenizer->token_to_id("[PAD]").has_value());
}

TEST_F(SerializationTest, InvalidFilePath) {
    auto model = create_test_bpe_model();
    
    // Test saving to invalid path
    EXPECT_THROW(model->save("/invalid/path/model.json"), std::runtime_error);
}

TEST_F(SerializationTest, EmptyModel) {
    // Test serialization of empty model
    auto empty_model = std::make_unique<models::BPE>();
    
    EXPECT_NO_THROW(empty_model->save("test_empty_model.json", true));
    
    // Verify file was created
    EXPECT_TRUE(std::filesystem::exists("test_empty_model.json"));
    
    // Clean up
    std::filesystem::remove("test_empty_model.json");
}

TEST_F(SerializationTest, PrettyPrintingOption) {
    auto model = create_test_bpe_model();
    
    // Save with pretty printing
    model->save("test_pretty.json", true);
    
    // Save without pretty printing
    model->save("test_compact.json", false);
    
    // Both files should exist
    EXPECT_TRUE(std::filesystem::exists("test_pretty.json"));
    EXPECT_TRUE(std::filesystem::exists("test_compact.json"));
    
    // Read both files
    std::ifstream pretty_file("test_pretty.json");
    std::ifstream compact_file("test_compact.json");
    
    std::string pretty_content((std::istreambuf_iterator<char>(pretty_file)),
                               std::istreambuf_iterator<char>());
    std::string compact_content((std::istreambuf_iterator<char>(compact_file)),
                                std::istreambuf_iterator<char>());
    
    // Pretty printed should generally be longer (more whitespace)
    // Note: This is a heuristic test since our JSON implementation might not differ much
    EXPECT_FALSE(pretty_content.empty());
    EXPECT_FALSE(compact_content.empty());
    
    // Clean up
    std::filesystem::remove("test_pretty.json");
    std::filesystem::remove("test_compact.json");
}

TEST_F(SerializationTest, ComponentSerialization) {
    // Test individual component serialization
    
    // Test normalizer
    auto normalizer = std::make_unique<normalizers::BertNormalizer>();
    // Note: Individual component serialization might not be implemented yet
    // This is a placeholder for future implementation
    
    // Test pre-tokenizer
    auto pre_tokenizer = std::make_unique<pre_tokenizers::Whitespace>();
    
    // Test post-processor
    auto post_processor = std::make_unique<processors::BertProcessing>();
    
    // Test decoder
    auto decoder = std::make_unique<decoders::WordPieceDecoder>();
    
    // For now, just verify they can be created
    EXPECT_NE(normalizer, nullptr);
    EXPECT_NE(pre_tokenizer, nullptr);
    EXPECT_NE(post_processor, nullptr);
    EXPECT_NE(decoder, nullptr);
}

TEST_F(SerializationTest, LargeVocabulary) {
    // Test serialization with a larger vocabulary
    models::BPE::Vocab large_vocab;
    models::BPE::Merges large_merges;
    
    // Create a larger vocabulary
    for (int i = 0; i < 1000; ++i) {
        std::string token = "token_" + std::to_string(i);
        large_vocab[token] = i;
        
        if (i > 0) {
            std::string prev_token = "token_" + std::to_string(i - 1);
            large_merges.push_back({prev_token, token});
        }
    }
    
    auto large_model = std::make_unique<models::BPE>(large_vocab, large_merges);
    
    // Test saving large model
    EXPECT_NO_THROW(large_model->save("test_large_model.json", false));
    
    // Verify file was created
    EXPECT_TRUE(std::filesystem::exists("test_large_model.json"));
    
    // Check file size is reasonable (should be substantial)
    auto file_size = std::filesystem::file_size("test_large_model.json");
    EXPECT_GT(file_size, 1000);  // Should be at least 1KB
    
    // Clean up
    std::filesystem::remove("test_large_model.json");
}
