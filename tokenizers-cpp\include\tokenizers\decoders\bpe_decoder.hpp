#pragma once

#include "decoder.hpp"
#include <string>
#include <vector>
#include <memory>

namespace tokenizers {
namespace decoders {

/**
 * @brief BPE Decoder
 * 
 * This decoder is responsible for decoding BPE tokens back to text.
 * It handles the merging of subword tokens and removal of special markers.
 */
class BPEDecoder : public Decoder {
private:
    /// Suffix to add to each token
    std::string suffix_;

public:
    /**
     * @brief Construct a new BPEDecoder
     * 
     * @param suffix The suffix to add to each token (default: "")
     */
    explicit BPEDecoder(const std::string& suffix = "");
    
    /**
     * @brief Builder class for BPEDecoder
     */
    class Builder {
    private:
        std::string suffix_ = "";
        
    public:
        Builder() = default;
        
        Builder& suffix(const std::string& suffix) { suffix_ = suffix; return *this; }
        
        BPEDecoder build() {
            return BPEDecoder(suffix_);
        }
    };
    
    /**
     * @brief Create a builder for BPEDecoder
     * 
     * @return Builder A BPEDecoder builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Decoder interface implementation
    std::string decode(const std::vector<std::string>& tokens) override;
    std::unique_ptr<Decoder> clone() const override;
    std::string get_type() const override { return "BPEDecoder"; }
    
    // BPEDecoder-specific methods
    
    /**
     * @brief Get the suffix
     * 
     * @return const std::string& The suffix
     */
    const std::string& get_suffix() const { return suffix_; }
    
    /**
     * @brief Set the suffix
     * 
     * @param suffix The suffix to set
     */
    void set_suffix(const std::string& suffix) { suffix_ = suffix; }
};

} // namespace decoders
} // namespace tokenizers
