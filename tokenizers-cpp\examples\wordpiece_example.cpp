#include <iostream>
#include <vector>
#include <string>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/wordpiece.hpp"
#include "tokenizers/trainers/wordpiece_trainer.hpp"

using namespace tokenizers;

int main() {
    std::cout << "WordPiece Tokenizer Example\n";
    std::cout << "===========================\n\n";
    
    try {
        // Example 1: Create a pre-trained WordPiece model
        std::cout << "Example 1: Pre-trained WordPiece Model\n";
        std::cout << "--------------------------------------\n";
        
        // Create a simple WordPiece vocabulary (similar to BERT)
        models::WordPiece::Vocab vocab = {
            // Special tokens
            {"[UNK]", 0}, {"[CLS]", 1}, {"[SEP]", 2}, {"[PAD]", 3}, {"[MASK]", 4},
            
            // Single characters
            {"a", 5}, {"b", 6}, {"c", 7}, {"d", 8}, {"e", 9}, {"f", 10}, {"g", 11}, {"h", 12},
            {"i", 13}, {"j", 14}, {"k", 15}, {"l", 16}, {"m", 17}, {"n", 18}, {"o", 19}, {"p", 20},
            {"q", 21}, {"r", 22}, {"s", 23}, {"t", 24}, {"u", 25}, {"v", 26}, {"w", 27}, {"x", 28},
            {"y", 29}, {"z", 30},
            
            // Common subwords with ## prefix
            {"##ing", 31}, {"##ed", 32}, {"##er", 33}, {"##est", 34}, {"##ly", 35}, {"##tion", 36},
            {"##al", 37}, {"##ness", 38}, {"##ment", 39}, {"##able", 40}, {"##ful", 41},
            
            // Common words
            {"the", 42}, {"and", 43}, {"is", 44}, {"in", 45}, {"to", 46}, {"of", 47}, {"a", 48},
            {"that", 49}, {"it", 50}, {"with", 51}, {"for", 52}, {"as", 53}, {"was", 54}, {"on", 55},
            {"are", 56}, {"you", 57}, {"this", 58}, {"be", 59}, {"at", 60}, {"have", 61}, {"or", 62},
            
            // Example words
            {"hello", 63}, {"world", 64}, {"test", 65}, {"example", 66}, {"word", 67}, {"piece", 68},
            {"token", 69}, {"##ize", 70}, {"##izer", 71}, {"##ization", 72}
        };
        
        auto wordpiece_model = std::make_unique<models::WordPiece>(vocab, "[UNK]", "##", 100);
        std::cout << "Created WordPiece model with vocabulary size: " << wordpiece_model->get_vocab_size() << "\n\n";
        
        // Create tokenizer
        Tokenizer tokenizer(std::move(wordpiece_model));
        
        // Test tokenization
        std::vector<std::string> test_sentences = {
            "hello world",
            "tokenization example",
            "testing wordpiece",
            "unknown words",
            "running quickly"
        };
        
        std::cout << "Tokenization Examples:\n";
        for (const auto& sentence : test_sentences) {
            std::cout << "Input: \"" << sentence << "\"\n";
            
            auto encoding = tokenizer.encode(sentence);
            
            const auto& tokens = encoding.get_tokens();
            const auto& ids = encoding.get_ids();
            
            std::cout << "Tokens: [";
            for (size_t i = 0; i < tokens.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << tokens[i] << "\"";
            }
            std::cout << "]\n";
            
            std::cout << "IDs: [";
            for (size_t i = 0; i < ids.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << ids[i];
            }
            std::cout << "]\n";
            
            // Test decoding
            auto decoded = tokenizer.decode(ids);
            std::cout << "Decoded: \"" << decoded << "\"\n\n";
        }
        
        // Example 2: Train a WordPiece model from scratch
        std::cout << "Example 2: Training WordPiece from Scratch\n";
        std::cout << "------------------------------------------\n";
        
        // Create training data
        std::vector<std::string> training_data = {
            "the quick brown fox jumps over the lazy dog",
            "hello world this is a test",
            "machine learning is fascinating",
            "natural language processing",
            "tokenization is important for nlp",
            "wordpiece algorithm works well",
            "subword tokenization helps with oov",
            "bert uses wordpiece tokenization",
            "transformers are powerful models",
            "attention is all you need",
            "the cat sat on the mat",
            "running jumping playing games",
            "quickly slowly carefully",
            "beautiful wonderful amazing",
            "testing training validation"
        };
        
        std::cout << "Training data contains " << training_data.size() << " sentences\n\n";
        
        // Create a WordPiece trainer
        auto trainer = trainers::WordPieceTrainer::builder()
            .vocab_size(200)  // Small vocabulary for demonstration
            .min_frequency(2)
            .show_progress(true)
            .continuing_subword_prefix("##")
            .max_input_chars_per_word(50)
            .special_tokens({
                AddedToken::from("[UNK]", true),
                AddedToken::from("[CLS]", true),
                AddedToken::from("[SEP]", true),
                AddedToken::from("[PAD]", true),
                AddedToken::from("[MASK]", true)
            })
            .build();
        
        std::cout << "Created WordPiece trainer with:\n";
        std::cout << "- Target vocabulary size: " << trainer.get_vocab_size() << "\n";
        std::cout << "- Minimum frequency: " << trainer.get_min_frequency() << "\n";
        std::cout << "- Special tokens: " << trainer.get_special_tokens().size() << "\n";
        std::cout << "- Continuing prefix: \"" << trainer.get_continuing_subword_prefix() << "\"\n\n";
        
        // Create an empty WordPiece model
        auto fresh_model = std::make_unique<models::WordPiece>();
        
        // Create tokenizer with the empty model
        Tokenizer training_tokenizer(std::move(fresh_model));
        
        std::cout << "Starting training...\n";
        std::cout << "===================\n";
        
        // Train the tokenizer
        training_tokenizer.train(trainer, training_data);
        
        std::cout << "\nTraining completed!\n\n";
        
        // Test the trained tokenizer
        std::cout << "Testing the trained tokenizer:\n";
        std::cout << "==============================\n";
        
        std::vector<std::string> test_sentences_trained = {
            "hello world",
            "machine learning",
            "wordpiece tokenization",
            "testing the model",
            "beautiful day"
        };
        
        for (const auto& sentence : test_sentences_trained) {
            std::cout << "Input: \"" << sentence << "\"\n";
            
            auto encoding = training_tokenizer.encode(sentence);
            
            const auto& tokens = encoding.get_tokens();
            const auto& ids = encoding.get_ids();
            
            std::cout << "Tokens: [";
            for (size_t i = 0; i < tokens.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << tokens[i] << "\"";
            }
            std::cout << "]\n";
            
            std::cout << "IDs: [";
            for (size_t i = 0; i < ids.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << ids[i];
            }
            std::cout << "]\n";
            
            // Test decoding
            auto decoded = training_tokenizer.decode(ids);
            std::cout << "Decoded: \"" << decoded << "\"\n\n";
        }
        
        // Show vocabulary information
        std::cout << "Vocabulary Information:\n";
        std::cout << "======================\n";
        std::cout << "Final vocabulary size: " << training_tokenizer.get_vocab_size() << "\n";
        
        // Show some vocabulary entries
        auto vocab_map = training_tokenizer.get_vocab();
        std::cout << "Sample vocabulary entries:\n";
        
        int count = 0;
        for (const auto& [token, id] : vocab_map) {
            if (count >= 20) break;  // Show first 20 entries
            std::cout << "\"" << token << "\" -> " << id << "\n";
            count++;
        }
        
        if (vocab_map.size() > 20) {
            std::cout << "... and " << (vocab_map.size() - 20) << " more entries\n";
        }
        
        std::cout << "\nWordPiece example completed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
