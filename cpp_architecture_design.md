# C++ Tokenizers Architecture Design

## Overview
This document outlines the design for converting the Rust tokenizers library to C++. The architecture maintains the same modular design with clear separation of concerns.

## Core Architecture

### 1. Core Data Structures

#### Token
```cpp
struct Token {
    uint32_t id;
    std::string value;
    std::pair<size_t, size_t> offsets;
    
    Token(uint32_t id, const std::string& value, std::pair<size_t, size_t> offsets);
};
```

#### Encoding
```cpp
class Encoding {
private:
    std::vector<uint32_t> ids_;
    std::vector<uint32_t> type_ids_;
    std::vector<std::string> tokens_;
    std::vector<std::optional<uint32_t>> words_;
    std::vector<std::pair<size_t, size_t>> offsets_;
    std::vector<uint32_t> special_tokens_mask_;
    std::vector<uint32_t> attention_mask_;
    std::vector<Encoding> overflowing_;
    std::unordered_map<size_t, std::pair<size_t, size_t>> sequence_ranges_;

public:
    // Constructors and getters/setters
    const std::vector<uint32_t>& get_ids() const;
    const std::vector<std::string>& get_tokens() const;
    // ... other methods
};
```

#### AddedToken
```cpp
struct AddedToken {
    std::string content;
    bool single_word = false;
    bool lstrip = false;
    bool rstrip = false;
    bool normalized = true;
    bool special = false;
    
    static AddedToken from(const std::string& content, bool special);
    AddedToken& single_word(bool single_word);
    AddedToken& lstrip(bool lstrip);
    AddedToken& rstrip(bool rstrip);
    AddedToken& normalized(bool normalized);
    AddedToken& special(bool special);
};
```

### 2. Core Interfaces

#### Model Interface
```cpp
class Model {
public:
    virtual ~Model() = default;
    virtual std::vector<Token> tokenize(const std::string& sequence) = 0;
    virtual std::optional<uint32_t> token_to_id(const std::string& token) const = 0;
    virtual std::optional<std::string> id_to_token(uint32_t id) const = 0;
    virtual std::unordered_map<std::string, uint32_t> get_vocab() const = 0;
    virtual size_t get_vocab_size() const = 0;
    virtual std::unique_ptr<Trainer> get_trainer() = 0;
};
```

#### Normalizer Interface
```cpp
class Normalizer {
public:
    virtual ~Normalizer() = default;
    virtual void normalize(NormalizedString& normalized) = 0;
};
```

#### PreTokenizer Interface
```cpp
class PreTokenizer {
public:
    virtual ~PreTokenizer() = default;
    virtual void pre_tokenize(PreTokenizedString& pretokenized) = 0;
};
```

#### PostProcessor Interface
```cpp
class PostProcessor {
public:
    virtual ~PostProcessor() = default;
    virtual size_t added_tokens(bool is_pair) const = 0;
    virtual Encoding process(const Encoding& encoding, 
                           const std::optional<Encoding>& pair = std::nullopt,
                           bool add_special_tokens = true) = 0;
};
```

#### Decoder Interface
```cpp
class Decoder {
public:
    virtual ~Decoder() = default;
    virtual std::string decode(const std::vector<std::string>& tokens) = 0;
    virtual std::vector<std::string> decode_chain(const std::vector<std::string>& tokens) = 0;
};
```

#### Trainer Interface
```cpp
class Trainer {
public:
    virtual ~Trainer() = default;
    virtual void train(Model& model) = 0;
    virtual void feed(const std::vector<std::string>& sequences) = 0;
};
```

### 3. Main Tokenizer Class

```cpp
class Tokenizer {
private:
    std::unique_ptr<Model> model_;
    std::unique_ptr<Normalizer> normalizer_;
    std::unique_ptr<PreTokenizer> pre_tokenizer_;
    std::unique_ptr<PostProcessor> post_processor_;
    std::unique_ptr<Decoder> decoder_;
    
    AddedVocabulary added_vocabulary_;
    std::optional<TruncationParams> truncation_;
    std::optional<PaddingParams> padding_;

public:
    explicit Tokenizer(std::unique_ptr<Model> model);
    
    // Configuration methods
    void set_normalizer(std::unique_ptr<Normalizer> normalizer);
    void set_pre_tokenizer(std::unique_ptr<PreTokenizer> pre_tokenizer);
    void set_post_processor(std::unique_ptr<PostProcessor> post_processor);
    void set_decoder(std::unique_ptr<Decoder> decoder);
    
    // Core functionality
    Encoding encode(const std::string& sequence, 
                   const std::optional<std::string>& pair = std::nullopt,
                   bool add_special_tokens = true);
    std::vector<Encoding> encode_batch(const std::vector<std::string>& sequences,
                                     bool add_special_tokens = true);
    std::string decode(const std::vector<uint32_t>& ids, bool skip_special_tokens = true);
    std::vector<std::string> decode_batch(const std::vector<std::vector<uint32_t>>& sequences,
                                        bool skip_special_tokens = true);
    
    // Training
    void train_from_files(Trainer& trainer, const std::vector<std::string>& files);
    
    // Serialization
    void save(const std::string& path, bool pretty = false);
    static std::unique_ptr<Tokenizer> from_file(const std::string& path);
    
    // Vocabulary management
    size_t add_tokens(const std::vector<std::string>& tokens);
    size_t add_special_tokens(const std::vector<AddedToken>& tokens);
};
```

### 4. Model Implementations

#### BPE Model
```cpp
class BPE : public Model {
private:
    std::unordered_map<std::string, uint32_t> vocab_;
    std::unordered_map<uint32_t, std::string> vocab_r_;
    std::unordered_map<std::pair<std::string, std::string>, std::pair<uint32_t, uint32_t>> merges_;
    std::optional<float> dropout_;
    std::optional<std::string> unk_token_;
    
public:
    static BPE from_file(const std::string& vocab_file, const std::string& merges_file);
    
    // Model interface implementation
    std::vector<Token> tokenize(const std::string& sequence) override;
    std::optional<uint32_t> token_to_id(const std::string& token) const override;
    std::optional<std::string> id_to_token(uint32_t id) const override;
    std::unordered_map<std::string, uint32_t> get_vocab() const override;
    size_t get_vocab_size() const override;
    std::unique_ptr<Trainer> get_trainer() override;
};
```

#### WordPiece Model
```cpp
class WordPiece : public Model {
private:
    std::unordered_map<std::string, uint32_t> vocab_;
    std::unordered_map<uint32_t, std::string> vocab_r_;
    std::string unk_token_;
    std::string continuing_subword_prefix_;
    size_t max_input_chars_per_word_;
    
public:
    static WordPiece from_file(const std::string& vocab_file);
    
    // Model interface implementation
    std::vector<Token> tokenize(const std::string& sequence) override;
    // ... other methods
};
```

## Dependencies

### Required Libraries
1. **nlohmann/json** - For JSON serialization/deserialization
2. **Google Test** - For unit testing
3. **CMake** - Build system
4. **Standard C++17** - Core language features

### Optional Libraries
1. **ICU** - For Unicode normalization (alternative: custom implementation)
2. **RE2** - For regex operations (alternative: std::regex)

## Project Structure
```
tokenizers-cpp/
├── CMakeLists.txt
├── include/
│   └── tokenizers/
│       ├── tokenizer.hpp
│       ├── models/
│       ├── normalizers/
│       ├── pre_tokenizers/
│       ├── processors/
│       ├── decoders/
│       └── utils/
├── src/
│   ├── tokenizer.cpp
│   ├── models/
│   ├── normalizers/
│   ├── pre_tokenizers/
│   ├── processors/
│   ├── decoders/
│   └── utils/
├── tests/
│   ├── test_tokenizer.cpp
│   ├── test_models.cpp
│   └── ...
├── examples/
│   ├── basic_usage.cpp
│   └── training_example.cpp
└── data/
    └── test_files/
```

## Implementation Strategy

1. **Phase 1**: Core data structures and interfaces
2. **Phase 2**: Basic tokenizer framework
3. **Phase 3**: BPE model implementation
4. **Phase 4**: WordPiece model implementation
5. **Phase 5**: Normalizers, PreTokenizers, PostProcessors
6. **Phase 6**: Decoders and serialization
7. **Phase 7**: Training functionality
8. **Phase 8**: Test conversion and validation

This design maintains the flexibility and modularity of the original Rust implementation while leveraging C++ idioms and best practices.
