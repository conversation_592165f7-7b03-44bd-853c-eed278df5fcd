#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include "tokenizers/pre_tokenizers/whitespace.hpp"

using namespace tokenizers;

int main() {
    std::cout << "Simple PreTokenizer Test\n";
    std::cout << "========================\n\n";
    
    try {
        std::cout << "Creating Whitespace PreTokenizer...\n";
        auto whitespace_pretokenizer = std::make_unique<pre_tokenizers::Whitespace>();
        std::cout << "PreTokenizer created successfully.\n\n";
        
        std::string test_text = "Hello world test";
        std::cout << "Testing with: \"" << test_text << "\"\n";
        
        std::cout << "Calling pre_tokenize_str...\n";
        auto splits = whitespace_pretokenizer->pre_tokenize_str(test_text);
        std::cout << "pre_tokenize_str completed.\n";
        
        std::cout << "Number of splits: " << splits.size() << "\n";
        
        std::cout << "Splits: [";
        for (size_t i = 0; i < splits.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << "\"" << splits[i] << "\"";
        }
        std::cout << "]\n";
        
        std::cout << "\nTest completed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
