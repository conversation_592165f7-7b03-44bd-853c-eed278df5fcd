#include "tokenizers/trainers/wordpiece_trainer.hpp"
#include "tokenizers/models/wordpiece.hpp"
#include <iostream>
#include <algorithm>
#include <sstream>
#include <regex>
#include <cmath>

namespace tokenizers {
namespace trainers {

WordPieceTrainer::WordPieceTrainer(size_t vocab_size,
                                   size_t min_frequency,
                                   const std::vector<AddedToken>& special_tokens,
                                   bool show_progress,
                                   const std::string& continuing_subword_prefix,
                                   size_t max_input_chars_per_word)
    : vocab_size_(vocab_size)
    , min_frequency_(min_frequency)
    , special_tokens_(special_tokens)
    , show_progress_(show_progress)
    , continuing_subword_prefix_(continuing_subword_prefix)
    , max_input_chars_per_word_(max_input_chars_per_word) {
}

std::vector<AddedToken> WordPieceTrainer::train(models::Model& model) {
    auto* wordpiece_model = dynamic_cast<models::WordPiece*>(&model);
    if (!wordpiece_model) {
        throw std::runtime_error("WordPieceTrainer can only train WordPiece models");
    }
    
    if (show_progress_) {
        std::cout << "Training WordPiece with vocab_size=" << vocab_size_ 
                  << ", min_frequency=" << min_frequency_ << std::endl;
    }
    
    // Compute initial alphabet
    compute_alphabet();
    
    // Build initial vocabulary
    models::WordPiece::Vocab vocab;
    uint32_t next_id = 0;
    
    // Add special tokens first
    for (const auto& special_token : special_tokens_) {
        vocab[special_token.content] = next_id++;
    }
    
    // Add alphabet
    for (const auto& char_str : initial_alphabet_) {
        if (vocab.find(char_str) == vocab.end()) {
            vocab[char_str] = next_id++;
        }
    }
    
    // Generate all possible subwords and count their frequencies
    std::unordered_map<std::string, size_t> subword_counts;
    
    for (const auto& [word, freq] : word_freqs_) {
        if (word.length() <= max_input_chars_per_word_) {
            auto subwords = generate_subwords(word);
            for (const auto& subword : subwords) {
                subword_counts[subword] += freq;
            }
        }
    }
    
    // Create candidate list sorted by score
    std::vector<std::pair<std::string, double>> candidates;
    for (const auto& [subword, count] : subword_counts) {
        if (count >= min_frequency_ && vocab.find(subword) == vocab.end()) {
            double score = calculate_score(subword, subword_counts);
            candidates.emplace_back(subword, score);
        }
    }
    
    // Sort by score (descending)
    std::sort(candidates.begin(), candidates.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    // Add top candidates to vocabulary
    for (const auto& [subword, score] : candidates) {
        if (vocab.size() >= vocab_size_) {
            break;
        }
        
        vocab[subword] = next_id++;
        
        if (show_progress_ && vocab.size() % 1000 == 0) {
            std::cout << "Added " << vocab.size() << " tokens to vocabulary..." << std::endl;
        }
    }
    
    if (show_progress_) {
        std::cout << "Finished training. Final vocab size: " << vocab.size() << std::endl;
    }
    
    // Update the WordPiece model
    *wordpiece_model = models::WordPiece(std::move(vocab), "[UNK]", continuing_subword_prefix_, max_input_chars_per_word_);
    
    return special_tokens_;
}

void WordPieceTrainer::feed(const std::vector<std::string>& sequences,
                           std::function<std::vector<std::string>(const std::string&)> process_func) {
    for (const auto& sequence : sequences) {
        if (process_func) {
            auto processed = process_func(sequence);
            for (const auto& word : processed) {
                word_freqs_[word]++;
            }
        } else {
            // Simple whitespace tokenization
            std::istringstream iss(sequence);
            std::string word;
            while (iss >> word) {
                // Remove punctuation and convert to lowercase for training
                std::regex punct_regex("[^a-zA-Z0-9]");
                word = std::regex_replace(word, punct_regex, "");
                if (!word.empty()) {
                    std::transform(word.begin(), word.end(), word.begin(), ::tolower);
                    word_freqs_[word]++;
                }
            }
        }
    }
}

std::unique_ptr<Trainer> WordPieceTrainer::clone() const {
    return std::make_unique<WordPieceTrainer>(vocab_size_, min_frequency_, special_tokens_,
                                             show_progress_, continuing_subword_prefix_, max_input_chars_per_word_);
}

void WordPieceTrainer::compute_alphabet() {
    initial_alphabet_.clear();
    
    for (const auto& [word, freq] : word_freqs_) {
        for (char c : word) {
            initial_alphabet_.insert(std::string(1, c));
        }
    }
}

std::vector<std::string> WordPieceTrainer::generate_subwords(const std::string& word) const {
    std::vector<std::string> subwords;
    
    // Generate all possible substrings
    for (size_t start = 0; start < word.length(); ++start) {
        for (size_t end = start + 1; end <= word.length(); ++end) {
            std::string subword = word.substr(start, end - start);
            
            // Add continuing prefix if not at the beginning
            if (start > 0) {
                subword = continuing_subword_prefix_ + subword;
            }
            
            subwords.push_back(subword);
        }
    }
    
    return subwords;
}

double WordPieceTrainer::calculate_score(const std::string& subword,
                                        const std::unordered_map<std::string, size_t>& subword_counts) const {
    // Simple frequency-based scoring
    // In practice, WordPiece uses more sophisticated scoring based on
    // the likelihood of the subword improving the overall model
    
    auto it = subword_counts.find(subword);
    if (it == subword_counts.end()) {
        return 0.0;
    }
    
    double freq = static_cast<double>(it->second);
    double length_penalty = 1.0 / subword.length();  // Prefer longer subwords
    
    // Remove continuing prefix for length calculation
    std::string actual_subword = subword;
    if (actual_subword.length() >= continuing_subword_prefix_.length() &&
        actual_subword.substr(0, continuing_subword_prefix_.length()) == continuing_subword_prefix_) {
        actual_subword = actual_subword.substr(continuing_subword_prefix_.length());
    }
    
    // Score is frequency weighted by length
    return freq * std::log(actual_subword.length() + 1);
}

} // namespace trainers
} // namespace tokenizers
