{"name": "create-wasm-app", "version": "0.1.0", "description": "create an app to consume rust-generated wasm packages", "main": "index.js", "bin": {"create-wasm-app": ".bin/create-wasm-app.js"}, "scripts": {"build": "webpack --config webpack.config.js", "start": "NODE_OPTIONS=--openssl-legacy-provider webpack-dev-server"}, "repository": {"type": "git", "url": "git+https://github.com/rustwasm/create-wasm-app.git"}, "keywords": ["webassembly", "wasm", "rust", "webpack"], "author": "<PERSON> <<EMAIL>>", "license": "(MIT OR Apache-2.0)", "bugs": {"url": "https://github.com/rustwasm/create-wasm-app/issues"}, "homepage": "https://github.com/rustwasm/create-wasm-app#readme", "devDependencies": {"copy-webpack-plugin": "^11.0.0", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.10.0"}, "dependencies": {"unstable_wasm": "file:../pkg"}}