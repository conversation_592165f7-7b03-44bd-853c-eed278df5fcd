#include "tokenizers/normalizers/bert_normalizer.hpp"
#include <algorithm>
#include <cctype>
#include <sstream>

namespace tokenizers {

// Simple NormalizedString implementation for now
class NormalizedString {
public:
    explicit NormalizedString(const std::string& text) : text_(text) {}
    
    const std::string& get() const { return text_; }
    void set(const std::string& text) { text_ = text; }
    
private:
    std::string text_;
};

namespace normalizers {

BertNormalizer::BertNormalizer(bool clean_text,
                               bool handle_chinese_chars,
                               bool strip_accents,
                               bool lowercase)
    : clean_text_(clean_text)
    , handle_chinese_chars_(handle_chinese_chars)
    , strip_accents_(strip_accents)
    , lowercase_(lowercase) {
}

void BertNormalizer::normalize(NormalizedString& normalized) {
    std::string text = normalized.get();
    
    // Apply normalization steps in order
    if (clean_text_) {
        text = clean_text(text);
    }
    
    if (handle_chinese_chars_) {
        text = handle_chinese_chars(text);
    }
    
    if (strip_accents_) {
        text = strip_accents(text);
    }
    
    if (lowercase_) {
        text = to_lowercase(text);
    }
    
    normalized.set(text);
}

std::unique_ptr<Normalizer> BertNormalizer::clone() const {
    return std::make_unique<BertNormalizer>(clean_text_, handle_chinese_chars_, strip_accents_, lowercase_);
}

std::string BertNormalizer::clean_text(const std::string& text) const {
    std::string result;
    result.reserve(text.length());
    
    for (char c : text) {
        // Skip control characters except tab, newline, and carriage return
        if (is_control(c) && c != '\t' && c != '\n' && c != '\r') {
            continue;
        }
        
        // Normalize whitespace
        if (is_whitespace(c)) {
            result += ' ';
        } else {
            result += c;
        }
    }
    
    return result;
}

std::string BertNormalizer::handle_chinese_chars(const std::string& text) const {
    std::string result;
    result.reserve(text.length() * 2);  // Reserve extra space for potential spaces
    
    for (size_t i = 0; i < text.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(text[i]);
        
        if (is_chinese_char(c)) {
            // Add spaces around Chinese characters
            if (!result.empty() && result.back() != ' ') {
                result += ' ';
            }
            result += c;
            if (i + 1 < text.length() && !is_whitespace(text[i + 1])) {
                result += ' ';
            }
        } else {
            result += c;
        }
    }
    
    return result;
}

std::string BertNormalizer::strip_accents(const std::string& text) const {
    // Simple accent stripping - in a full implementation, this would use Unicode normalization
    // For now, we'll just handle basic Latin accents
    std::string result;
    result.reserve(text.length());
    
    for (unsigned char c : text) {
        // Basic accent removal for common Latin characters
        switch (c) {
            case 0xC0: case 0xC1: case 0xC2: case 0xC3: case 0xC4: case 0xC5:  // À-Å
                result += 'A'; break;
            case 0xE0: case 0xE1: case 0xE2: case 0xE3: case 0xE4: case 0xE5:  // à-å
                result += 'a'; break;
            case 0xC8: case 0xC9: case 0xCA: case 0xCB:  // È-Ë
                result += 'E'; break;
            case 0xE8: case 0xE9: case 0xEA: case 0xEB:  // è-ë
                result += 'e'; break;
            case 0xCC: case 0xCD: case 0xCE: case 0xCF:  // Ì-Ï
                result += 'I'; break;
            case 0xEC: case 0xED: case 0xEE: case 0xEF:  // ì-ï
                result += 'i'; break;
            case 0xD2: case 0xD3: case 0xD4: case 0xD5: case 0xD6:  // Ò-Ö
                result += 'O'; break;
            case 0xF2: case 0xF3: case 0xF4: case 0xF5: case 0xF6:  // ò-ö
                result += 'o'; break;
            case 0xD9: case 0xDA: case 0xDB: case 0xDC:  // Ù-Ü
                result += 'U'; break;
            case 0xF9: case 0xFA: case 0xFB: case 0xFC:  // ù-ü
                result += 'u'; break;
            case 0xC7:  // Ç
                result += 'C'; break;
            case 0xE7:  // ç
                result += 'c'; break;
            case 0xD1:  // Ñ
                result += 'N'; break;
            case 0xF1:  // ñ
                result += 'n'; break;
            default:
                result += c; break;
        }
    }
    
    return result;
}

std::string BertNormalizer::to_lowercase(const std::string& text) const {
    std::string result = text;
    std::transform(result.begin(), result.end(), result.begin(), 
                   [](unsigned char c) { return std::tolower(c); });
    return result;
}

bool BertNormalizer::is_control(char c) const {
    // Control characters are in the range 0x00-0x1F and 0x7F-0x9F
    unsigned char uc = static_cast<unsigned char>(c);
    return (uc <= 0x1F) || (uc >= 0x7F && uc <= 0x9F);
}

bool BertNormalizer::is_whitespace(char c) const {
    // Standard whitespace characters
    return c == ' ' || c == '\t' || c == '\n' || c == '\r' || c == '\f' || c == '\v';
}

bool BertNormalizer::is_chinese_char(unsigned char c) const {
    // Simplified check for Chinese characters
    // In a full implementation, this would check Unicode ranges for CJK characters
    // For now, we'll check some common extended ASCII ranges that might contain Chinese
    // This is a very basic implementation and would need proper Unicode support
    return c >= 0x80;  // Very basic check - anything in extended ASCII range
}

} // namespace normalizers
} // namespace tokenizers
