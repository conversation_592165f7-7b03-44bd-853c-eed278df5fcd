#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <sstream>
#include <stdexcept>

namespace tokenizers {
namespace utils {

/**
 * @brief Simple JSON utility class for basic serialization/deserialization
 * 
 * This is a minimal JSON implementation to avoid external dependencies.
 * It supports basic operations needed for tokenizer serialization.
 */
class SimpleJson {
public:
    /**
     * @brief Serialize a string-to-uint32 map to JSON
     * 
     * @param map The map to serialize
     * @param pretty Whether to format the output nicely
     * @return std::string JSON string
     */
    static std::string serialize_vocab(const std::unordered_map<std::string, uint32_t>& map, bool pretty = false);
    
    /**
     * @brief Serialize a vector of string pairs to JSON array
     * 
     * @param pairs The pairs to serialize
     * @param pretty Whether to format the output nicely
     * @return std::string JSON string
     */
    static std::string serialize_merges(const std::vector<std::pair<std::string, std::string>>& pairs, bool pretty = false);
    
    /**
     * @brief Parse a simple JSON vocab object
     * 
     * @param json_str The JSON string to parse
     * @return std::unordered_map<std::string, uint32_t> The parsed vocabulary
     */
    static std::unordered_map<std::string, uint32_t> parse_vocab(const std::string& json_str);
    
    /**
     * @brief Parse a simple JSON array of merge pairs
     * 
     * @param json_str The JSON string to parse
     * @return std::vector<std::pair<std::string, std::string>> The parsed merges
     */
    static std::vector<std::pair<std::string, std::string>> parse_merges(const std::string& json_str);
    
    /**
     * @brief Escape a string for JSON
     * 
     * @param str The string to escape
     * @return std::string The escaped string
     */
    static std::string escape_string(const std::string& str);
    
    /**
     * @brief Unescape a JSON string
     * 
     * @param str The escaped string
     * @return std::string The unescaped string
     */
    static std::string unescape_string(const std::string& str);

private:
    /**
     * @brief Skip whitespace in a string
     * 
     * @param str The string
     * @param pos The current position (modified)
     */
    static void skip_whitespace(const std::string& str, size_t& pos);
    
    /**
     * @brief Parse a JSON string value
     * 
     * @param str The JSON string
     * @param pos The current position (modified)
     * @return std::string The parsed string
     */
    static std::string parse_string(const std::string& str, size_t& pos);
    
    /**
     * @brief Parse a JSON number value
     * 
     * @param str The JSON string
     * @param pos The current position (modified)
     * @return uint32_t The parsed number
     */
    static uint32_t parse_number(const std::string& str, size_t& pos);
    
    /**
     * @brief Expect a specific character
     * 
     * @param str The JSON string
     * @param pos The current position (modified)
     * @param expected The expected character
     */
    static void expect_char(const std::string& str, size_t& pos, char expected);
};

} // namespace utils
} // namespace tokenizers
