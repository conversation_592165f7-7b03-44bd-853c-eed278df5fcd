{"name": "tokenizers-android-arm-eabi", "version": "0.13.4-rc1", "os": ["android"], "cpu": ["arm"], "main": "tokenizers.android-arm-eabi.node", "files": ["tokenizers.android-arm-eabi.node"], "description": "Tokenizers platform specific bindings", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api"], "license": "MIT", "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": "tokenizers"}