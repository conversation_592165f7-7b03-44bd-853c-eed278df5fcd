#include <gtest/gtest.h>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/models/wordpiece.hpp"
#include "tokenizers/trainers/bpe_trainer.hpp"
#include "tokenizers/trainers/wordpiece_trainer.hpp"
#include "tokenizers/pre_tokenizers/whitespace.hpp"
#include "tokenizers/normalizers/bert_normalizer.hpp"
#include <fstream>
#include <filesystem>

using namespace tokenizers;

class TrainingTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test data file
        create_test_data();
    }
    
    void TearDown() override {
        // Clean up test files
        cleanup_test_files();
    }
    
    void create_test_data() {
        std::ofstream file("test_training_data.txt");
        file << "Hello world this is a test\n";
        file << "The quick brown fox jumps over the lazy dog\n";
        file << "Machine learning is fascinating\n";
        file << "Natural language processing with transformers\n";
        file << "Tokenization is important for NLP\n";
        file << "BPE and WordPiece are popular algorithms\n";
        file << "Training tokenizers from scratch\n";
        file << "Subword tokenization helps with vocabulary\n";
        file << "Out of vocabulary words are handled well\n";
        file << "This is test data for training\n";
        file << "More sentences for better training\n";
        file << "Testing the training functionality\n";
        file << "Comprehensive test coverage is important\n";
        file << "Quality assurance through testing\n";
        file << "Automated testing saves time\n";
    }
    
    void cleanup_test_files() {
        std::filesystem::remove("test_training_data.txt");
        std::filesystem::remove("test_tokenizer.json");
    }
    
    std::vector<std::string> load_training_data() {
        std::vector<std::string> data;
        std::ifstream file("test_training_data.txt");
        std::string line;
        while (std::getline(file, line)) {
            if (!line.empty()) {
                data.push_back(line);
            }
        }
        return data;
    }
};

TEST_F(TrainingTest, BPETrainingBasic) {
    // Create an empty BPE model
    auto model = std::make_unique<models::BPE>();
    Tokenizer tokenizer(std::move(model));
    
    // Create BPE trainer
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(100)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({
            AddedToken::from("[UNK]", true),
            AddedToken::from("[PAD]", true),
            AddedToken::from("[CLS]", true),
            AddedToken::from("[SEP]", true)
        })
        .build();
    
    // Load training data
    auto training_data = load_training_data();
    ASSERT_FALSE(training_data.empty());
    
    // Train the tokenizer
    tokenizer.train(trainer, training_data);
    
    // Check that vocabulary was built
    EXPECT_GT(tokenizer.get_vocab_size(), 4);  // At least the special tokens
    EXPECT_LE(tokenizer.get_vocab_size(), 100);  // Should not exceed target size
    
    // Test that special tokens are present
    EXPECT_TRUE(tokenizer.token_to_id("[UNK]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[PAD]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[CLS]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[SEP]").has_value());
}

TEST_F(TrainingTest, BPETrainingWithDropout) {
    // Create BPE model with dropout
    auto model = std::make_unique<models::BPE>();
    model->set_dropout(0.1);
    Tokenizer tokenizer(std::move(model));
    
    // Create trainer
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(50)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({AddedToken::from("[UNK]", true)})
        .build();
    
    // Train
    auto training_data = load_training_data();
    tokenizer.train(trainer, training_data);
    
    // Check that model retains dropout setting
    // Note: This requires access to the model's dropout setting
    // In a full implementation, we might need a getter for this
    EXPECT_GT(tokenizer.get_vocab_size(), 1);
}

TEST_F(TrainingTest, BPETrainingWithPreTokenizer) {
    // Create BPE model
    auto model = std::make_unique<models::BPE>();
    Tokenizer tokenizer(std::move(model));
    
    // Add pre-tokenizer
    tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::Whitespace>());
    
    // Create trainer
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(80)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({AddedToken::from("[UNK]", true)})
        .build();
    
    // Train
    auto training_data = load_training_data();
    tokenizer.train(trainer, training_data);
    
    // Test tokenization after training
    auto encoding = tokenizer.encode("Hello world test");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_FALSE(tokens.empty());
    
    // Verify that tokens are reasonable
    for (const auto& token : tokens) {
        EXPECT_FALSE(token.empty());
    }
}

TEST_F(TrainingTest, WordPieceTraining) {
    // Create empty WordPiece model
    auto model = std::make_unique<models::WordPiece>();
    Tokenizer tokenizer(std::move(model));
    
    // Create WordPiece trainer
    auto trainer = trainers::WordPieceTrainer::builder()
        .vocab_size(150)
        .min_frequency(1)
        .show_progress(false)
        .continuing_subword_prefix("##")
        .special_tokens({
            AddedToken::from("[UNK]", true),
            AddedToken::from("[CLS]", true),
            AddedToken::from("[SEP]", true),
            AddedToken::from("[PAD]", true),
            AddedToken::from("[MASK]", true)
        })
        .build();
    
    // Train
    auto training_data = load_training_data();
    tokenizer.train(trainer, training_data);
    
    // Check vocabulary
    EXPECT_GT(tokenizer.get_vocab_size(), 5);  // At least special tokens
    EXPECT_LE(tokenizer.get_vocab_size(), 150);
    
    // Test that special tokens are present
    EXPECT_TRUE(tokenizer.token_to_id("[UNK]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[CLS]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[SEP]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[PAD]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[MASK]").has_value());
}

TEST_F(TrainingTest, TrainingWithNormalizer) {
    // Create BPE model
    auto model = std::make_unique<models::BPE>();
    Tokenizer tokenizer(std::move(model));
    
    // Add normalizer
    tokenizer.set_normalizer(std::make_unique<normalizers::BertNormalizer>());
    
    // Create trainer
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(60)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({AddedToken::from("[UNK]", true)})
        .build();
    
    // Train with mixed case data
    std::vector<std::string> mixed_case_data = {
        "Hello WORLD",
        "This Is A Test",
        "UPPERCASE and lowercase",
        "MiXeD cAsE tExT"
    };
    
    tokenizer.train(trainer, mixed_case_data);
    
    // Test that normalization works during training
    auto encoding = tokenizer.encode("Hello WORLD");
    EXPECT_FALSE(encoding.is_empty());
}

TEST_F(TrainingTest, MinimumFrequencyFiltering) {
    // Create BPE model
    auto model = std::make_unique<models::BPE>();
    Tokenizer tokenizer(std::move(model));
    
    // Create trainer with higher minimum frequency
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(200)
        .min_frequency(3)  // Higher minimum frequency
        .show_progress(false)
        .special_tokens({AddedToken::from("[UNK]", true)})
        .build();
    
    // Create data with some rare tokens
    std::vector<std::string> data_with_rare_tokens = {
        "common word appears often",
        "common word appears often",
        "common word appears often",
        "rare token appears once",
        "another rare token here"
    };
    
    tokenizer.train(trainer, data_with_rare_tokens);
    
    // Common tokens should be in vocabulary
    auto encoding = tokenizer.encode("common word");
    EXPECT_FALSE(encoding.is_empty());
    
    // Vocabulary should be reasonable size
    EXPECT_GT(tokenizer.get_vocab_size(), 1);
}

TEST_F(TrainingTest, EmptyTrainingData) {
    auto model = std::make_unique<models::BPE>();
    Tokenizer tokenizer(std::move(model));
    
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(50)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({AddedToken::from("[UNK]", true)})
        .build();
    
    // Train with empty data
    std::vector<std::string> empty_data;
    tokenizer.train(trainer, empty_data);
    
    // Should still have special tokens
    EXPECT_TRUE(tokenizer.token_to_id("[UNK]").has_value());
}

TEST_F(TrainingTest, SingleSentenceTraining) {
    auto model = std::make_unique<models::BPE>();
    Tokenizer tokenizer(std::move(model));
    
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(30)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({AddedToken::from("[UNK]", true)})
        .build();
    
    // Train with single sentence
    std::vector<std::string> single_sentence = {"Hello world test"};
    tokenizer.train(trainer, single_sentence);
    
    // Should be able to tokenize the training sentence
    auto encoding = tokenizer.encode("Hello world test");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_FALSE(tokens.empty());
}

TEST_F(TrainingTest, VocabularySizeConstraint) {
    auto model = std::make_unique<models::BPE>();
    Tokenizer tokenizer(std::move(model));
    
    // Very small vocabulary size
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(10)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({
            AddedToken::from("[UNK]", true),
            AddedToken::from("[PAD]", true)
        })
        .build();
    
    auto training_data = load_training_data();
    tokenizer.train(trainer, training_data);
    
    // Vocabulary should not exceed the specified size
    EXPECT_LE(tokenizer.get_vocab_size(), 10);
    
    // Should still have special tokens
    EXPECT_TRUE(tokenizer.token_to_id("[UNK]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[PAD]").has_value());
}

TEST_F(TrainingTest, TrainingConsistency) {
    // Train two identical tokenizers and verify they produce the same results
    auto model1 = std::make_unique<models::BPE>();
    auto model2 = std::make_unique<models::BPE>();
    
    Tokenizer tokenizer1(std::move(model1));
    Tokenizer tokenizer2(std::move(model2));
    
    auto trainer1 = trainers::BPETrainer::builder()
        .vocab_size(50)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({AddedToken::from("[UNK]", true)})
        .build();
    
    auto trainer2 = trainers::BPETrainer::builder()
        .vocab_size(50)
        .min_frequency(1)
        .show_progress(false)
        .special_tokens({AddedToken::from("[UNK]", true)})
        .build();
    
    auto training_data = load_training_data();
    
    tokenizer1.train(trainer1, training_data);
    tokenizer2.train(trainer2, training_data);
    
    // Both should have the same vocabulary size
    EXPECT_EQ(tokenizer1.get_vocab_size(), tokenizer2.get_vocab_size());
    
    // Both should tokenize the same way
    std::string test_text = "Hello world test";
    auto encoding1 = tokenizer1.encode(test_text);
    auto encoding2 = tokenizer2.encode(test_text);
    
    EXPECT_EQ(encoding1.get_tokens().size(), encoding2.get_tokens().size());
}
