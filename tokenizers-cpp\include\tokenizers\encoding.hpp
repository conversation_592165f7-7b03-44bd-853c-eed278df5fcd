#pragma once

#include "token.hpp"
#include <vector>
#include <string>
#include <optional>
#include <unordered_map>
#include <utility>
#include <cstdint>

namespace tokenizers {

/**
 * @brief Represents the output of a Tokenizer
 * 
 * An Encoding contains all the information produced by the tokenization process:
 * - Token IDs and their corresponding tokens
 * - Type IDs for distinguishing sequences
 * - Word indices for tracking original words
 * - Character offsets for alignment with original text
 * - Special token and attention masks
 * - Overflow encodings when truncation occurs
 */
class Encoding {
private:
    /// IDs produced by the Tokenizer
    std::vector<uint32_t> ids_;
    
    /// Type of the IDs (for distinguishing sequences)
    std::vector<uint32_t> type_ids_;
    
    /// Tokens associated to each ID
    std::vector<std::string> tokens_;
    
    /// Index of the word associated to each token/ID
    std::vector<std::optional<uint32_t>> words_;
    
    /// Offsets of the token/ID from the original text
    std::vector<std::pair<size_t, size_t>> offsets_;
    
    /// Mask identifying special tokens
    std::vector<uint32_t> special_tokens_mask_;
    
    /// Mask identifying padding tokens for the attention mechanism
    std::vector<uint32_t> attention_mask_;
    
    /// A list of overflowing Encoding generated when we got truncated
    std::vector<Encoding> overflowing_;
    
    /// Ranges of tokens covered by each sequence
    std::unordered_map<size_t, std::pair<size_t, size_t>> sequence_ranges_;

public:
    /**
     * @brief Default constructor
     */
    Encoding() = default;
    
    /**
     * @brief Construct a new Encoding with all components
     * 
     * @param ids Token IDs
     * @param type_ids Type IDs
     * @param tokens Token strings
     * @param words Word indices
     * @param offsets Character offsets
     * @param special_tokens_mask Special token mask
     * @param attention_mask Attention mask
     * @param overflowing Overflow encodings
     * @param sequence_ranges Sequence ranges
     */
    Encoding(std::vector<uint32_t> ids,
             std::vector<uint32_t> type_ids,
             std::vector<std::string> tokens,
             std::vector<std::optional<uint32_t>> words,
             std::vector<std::pair<size_t, size_t>> offsets,
             std::vector<uint32_t> special_tokens_mask,
             std::vector<uint32_t> attention_mask,
             std::vector<Encoding> overflowing = {},
             std::unordered_map<size_t, std::pair<size_t, size_t>> sequence_ranges = {})
        : ids_(std::move(ids))
        , type_ids_(std::move(type_ids))
        , tokens_(std::move(tokens))
        , words_(std::move(words))
        , offsets_(std::move(offsets))
        , special_tokens_mask_(std::move(special_tokens_mask))
        , attention_mask_(std::move(attention_mask))
        , overflowing_(std::move(overflowing))
        , sequence_ranges_(std::move(sequence_ranges)) {}
    
    /**
     * @brief Construct an Encoding from a vector of tokens
     * 
     * @param tokens Vector of Token objects
     * @param type_id Type ID to assign to all tokens
     * @return Encoding The constructed encoding
     */
    static Encoding from_tokens(const std::vector<Token>& tokens, uint32_t type_id = 0);
    
    // Getters
    const std::vector<uint32_t>& get_ids() const { return ids_; }
    const std::vector<uint32_t>& get_type_ids() const { return type_ids_; }
    const std::vector<std::string>& get_tokens() const { return tokens_; }
    const std::vector<std::optional<uint32_t>>& get_words() const { return words_; }
    const std::vector<std::pair<size_t, size_t>>& get_offsets() const { return offsets_; }
    const std::vector<uint32_t>& get_special_tokens_mask() const { return special_tokens_mask_; }
    const std::vector<uint32_t>& get_attention_mask() const { return attention_mask_; }
    const std::vector<Encoding>& get_overflowing() const { return overflowing_; }
    const std::unordered_map<size_t, std::pair<size_t, size_t>>& get_sequence_ranges() const { return sequence_ranges_; }
    
    // Mutable getters
    std::vector<uint32_t>& get_ids() { return ids_; }
    std::vector<uint32_t>& get_type_ids() { return type_ids_; }
    std::vector<std::string>& get_tokens() { return tokens_; }
    std::vector<std::optional<uint32_t>>& get_words() { return words_; }
    std::vector<std::pair<size_t, size_t>>& get_offsets() { return offsets_; }
    std::vector<uint32_t>& get_special_tokens_mask() { return special_tokens_mask_; }
    std::vector<uint32_t>& get_attention_mask() { return attention_mask_; }
    std::vector<Encoding>& get_overflowing() { return overflowing_; }
    std::unordered_map<size_t, std::pair<size_t, size_t>>& get_sequence_ranges() { return sequence_ranges_; }

    // Setters
    void set_ids(const std::vector<uint32_t>& ids) { ids_ = ids; }
    void set_type_ids(const std::vector<uint32_t>& type_ids) { type_ids_ = type_ids; }
    void set_tokens(const std::vector<std::string>& tokens) { tokens_ = tokens; }
    void set_words(const std::vector<std::optional<uint32_t>>& words) { words_ = words; }
    void set_offsets(const std::vector<std::pair<size_t, size_t>>& offsets) { offsets_ = offsets; }
    void set_special_tokens_mask(const std::vector<uint32_t>& special_tokens_mask) { special_tokens_mask_ = special_tokens_mask; }
    void set_attention_mask(const std::vector<uint32_t>& attention_mask) { attention_mask_ = attention_mask; }
    void set_overflowing(const std::vector<Encoding>& overflowing) { overflowing_ = overflowing; }
    void set_sequence_ranges(const std::unordered_map<size_t, std::pair<size_t, size_t>>& sequence_ranges) { sequence_ranges_ = sequence_ranges; }
    
    /**
     * @brief Check if this Encoding is empty
     * 
     * @return true if the encoding contains no tokens, false otherwise
     */
    bool is_empty() const { return ids_.empty(); }
    
    /**
     * @brief Get the number of tokens in this encoding
     * 
     * @return size_t The number of tokens
     */
    size_t size() const { return ids_.size(); }
    
    /**
     * @brief Get the length of the encoding (same as size)
     * 
     * @return size_t The number of tokens
     */
    size_t length() const { return size(); }
    
    /**
     * @brief Get the number of sequences in this encoding
     * 
     * @return size_t The number of sequences
     */
    size_t n_sequences() const;
    
    /**
     * @brief Clear all data in this encoding
     */
    void clear();
    
    /**
     * @brief Pad this encoding to the specified length
     * 
     * @param length Target length
     * @param pad_id Padding token ID
     * @param pad_type_id Padding type ID
     * @param pad_token Padding token string
     * @param direction Padding direction ("left" or "right")
     */
    void pad(size_t length, 
             uint32_t pad_id = 0, 
             uint32_t pad_type_id = 0, 
             const std::string& pad_token = "[PAD]", 
             const std::string& direction = "right");
    
    /**
     * @brief Truncate this encoding to the specified length
     * 
     * @param length Maximum length
     * @param stride Stride for overflow
     * @param direction Truncation direction ("left" or "right")
     */
    void truncate(size_t length, size_t stride = 0, const std::string& direction = "right");
    
    /**
     * @brief Convert character position to token index
     * 
     * @param char_pos Character position
     * @param sequence_id Sequence ID (0 for first sequence, 1 for second)
     * @return std::optional<size_t> Token index if found
     */
    std::optional<size_t> char_to_token(size_t char_pos, size_t sequence_id = 0) const;
    
    /**
     * @brief Convert character position to word index
     * 
     * @param char_pos Character position
     * @param sequence_id Sequence ID (0 for first sequence, 1 for second)
     * @return std::optional<uint32_t> Word index if found
     */
    std::optional<uint32_t> char_to_word(size_t char_pos, size_t sequence_id = 0) const;
    
    /**
     * @brief Convert token index to character span
     * 
     * @param token_index Token index
     * @param sequence_id Sequence ID (0 for first sequence, 1 for second)
     * @return std::optional<std::pair<size_t, size_t>> Character span if found
     */
    std::optional<std::pair<size_t, size_t>> token_to_chars(size_t token_index, size_t sequence_id = 0) const;
    
    /**
     * @brief Convert word index to character span
     * 
     * @param word_index Word index
     * @param sequence_id Sequence ID (0 for first sequence, 1 for second)
     * @return std::optional<std::pair<size_t, size_t>> Character span if found
     */
    std::optional<std::pair<size_t, size_t>> word_to_chars(uint32_t word_index, size_t sequence_id = 0) const;
    
    /**
     * @brief Convert word index to token span
     * 
     * @param word_index Word index
     * @param sequence_id Sequence ID (0 for first sequence, 1 for second)
     * @return std::optional<std::pair<size_t, size_t>> Token span if found
     */
    std::optional<std::pair<size_t, size_t>> word_to_tokens(uint32_t word_index, size_t sequence_id = 0) const;
};

} // namespace tokenizers
