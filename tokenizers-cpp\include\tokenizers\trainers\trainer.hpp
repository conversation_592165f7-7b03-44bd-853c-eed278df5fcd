#pragma once

#include "../added_token.hpp"
#include <string>
#include <vector>
#include <memory>
#include <functional>

namespace tokenizers {

// Forward declarations
namespace models {
    class Model;
}

namespace trainers {

/**
 * @brief Base class for all model trainers
 * 
 * A Trainer is responsible for training a tokenization model from raw text data.
 * It processes training data and updates the model's vocabulary and parameters.
 */
class Trainer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~Trainer() = default;
    
    /**
     * @brief Train the model using the provided data
     * 
     * @param model The model to train
     * @return std::vector<AddedToken> Special tokens that were added during training
     */
    virtual std::vector<AddedToken> train(models::Model& model) = 0;
    
    /**
     * @brief Feed training data to the trainer
     * 
     * @param sequences The training sequences
     * @param process_func Function to process each sequence (e.g., for tokenization)
     */
    virtual void feed(const std::vector<std::string>& sequences,
                     std::function<std::vector<std::string>(const std::string&)> process_func = nullptr) = 0;
    
    /**
     * @brief Set the vocabulary size
     * 
     * @param vocab_size The target vocabulary size
     */
    virtual void set_vocab_size(size_t vocab_size) = 0;
    
    /**
     * @brief Get the vocabulary size
     * 
     * @return size_t The target vocabulary size
     */
    virtual size_t get_vocab_size() const = 0;
    
    /**
     * @brief Set the minimum frequency for tokens
     * 
     * @param min_frequency The minimum frequency threshold
     */
    virtual void set_min_frequency(size_t min_frequency) = 0;
    
    /**
     * @brief Get the minimum frequency
     * 
     * @return size_t The minimum frequency threshold
     */
    virtual size_t get_min_frequency() const = 0;
    
    /**
     * @brief Set special tokens
     * 
     * @param special_tokens The special tokens to add
     */
    virtual void set_special_tokens(const std::vector<AddedToken>& special_tokens) = 0;
    
    /**
     * @brief Get special tokens
     * 
     * @return const std::vector<AddedToken>& The special tokens
     */
    virtual const std::vector<AddedToken>& get_special_tokens() const = 0;
    
    /**
     * @brief Set whether to show progress during training
     * 
     * @param show_progress Whether to show progress
     */
    virtual void set_show_progress(bool show_progress) = 0;
    
    /**
     * @brief Get whether progress is shown
     * 
     * @return bool Whether progress is shown
     */
    virtual bool get_show_progress() const = 0;
    
    /**
     * @brief Clone this trainer
     * 
     * @return std::unique_ptr<Trainer> A copy of this trainer
     */
    virtual std::unique_ptr<Trainer> clone() const = 0;
    
    /**
     * @brief Get the trainer type name
     * 
     * @return std::string The trainer type
     */
    virtual std::string get_type() const = 0;
};

} // namespace trainers
} // namespace tokenizers
