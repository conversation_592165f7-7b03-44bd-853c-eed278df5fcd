#include "tokenizers/models/bpe.hpp"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <stdexcept>
#include <regex>
#include <random>
#include <unordered_set>

namespace tokenizers {

// Forward declaration
namespace trainers {
    class BPETrainer;
}

namespace models {

BPE::BPE() : fuse_unk_(false) {}

BPE::BPE(Vocab vocab,
         Merges merges,
         std::optional<float> dropout,
         std::optional<std::string> unk_token,
         std::optional<std::string> continuing_subword_prefix,
         std::optional<std::string> end_of_word_suffix,
         bool fuse_unk)
    : vocab_(std::move(vocab))
    , merges_(std::move(merges))
    , dropout_(dropout)
    , unk_token_(unk_token)
    , continuing_subword_prefix_(continuing_subword_prefix)
    , end_of_word_suffix_(end_of_word_suffix)
    , fuse_unk_(fuse_unk) {
    
    // Build reverse vocabulary
    for (const auto& [token, id] : vocab_) {
        vocab_r_[id] = token;
    }
    
    // Build merge map
    build_merge_map();
}

BPE BPE::from_file(const std::string& vocab_file, const std::string& merges_file) {
    // Load vocabulary from JSON file
    Vocab vocab;
    std::ifstream vocab_stream(vocab_file);
    if (!vocab_stream.is_open()) {
        throw std::runtime_error("Could not open vocabulary file: " + vocab_file);
    }
    
    // Simple JSON parsing for vocabulary (token: id pairs)
    std::string line;
    std::regex json_pair_regex("\"([^\"]+)\":\\s*(\\d+)");
    std::smatch match;
    
    while (std::getline(vocab_stream, line)) {
        if (std::regex_search(line, match, json_pair_regex)) {
            std::string token = match[1].str();
            uint32_t id = std::stoul(match[2].str());
            vocab[token] = id;
        }
    }
    
    // Load merges from text file
    Merges merges;
    std::ifstream merges_stream(merges_file);
    if (!merges_stream.is_open()) {
        throw std::runtime_error("Could not open merges file: " + merges_file);
    }
    
    while (std::getline(merges_stream, line)) {
        if (line.empty() || line[0] == '#') {
            continue;  // Skip empty lines and comments
        }
        
        std::istringstream iss(line);
        std::string first, second;
        if (iss >> first >> second) {
            merges.emplace_back(first, second);
        }
    }
    
    return BPE(std::move(vocab), std::move(merges));
}

BPE BPE::Builder::build() {
    if (!vocab_ || !merges_) {
        throw std::runtime_error("Both vocabulary and merges must be provided");
    }
    
    return BPE(*vocab_, *merges_, dropout_, unk_token_, 
              continuing_subword_prefix_, end_of_word_suffix_, fuse_unk_);
}

std::vector<Token> BPE::tokenize(const std::string& sequence) {
    if (sequence.empty()) {
        return {};
    }
    
    std::vector<Token> tokens;
    
    // Split sequence into words (simple whitespace splitting for now)
    std::istringstream iss(sequence);
    std::string word;
    size_t offset = 0;
    
    while (iss >> word) {
        // Find the actual position of the word in the original sequence
        size_t word_start = sequence.find(word, offset);
        if (word_start == std::string::npos) {
            word_start = offset;
        }
        
        // Apply BPE to the word
        auto subwords = merge_word(word);
        auto word_tokens = word_to_tokens(word, subwords, word_start);
        
        tokens.insert(tokens.end(), word_tokens.begin(), word_tokens.end());
        
        offset = word_start + word.length();
    }
    
    return tokens;
}

std::optional<uint32_t> BPE::token_to_id(const std::string& token) const {
    auto it = vocab_.find(token);
    return it != vocab_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::optional<std::string> BPE::id_to_token(uint32_t id) const {
    auto it = vocab_r_.find(id);
    return it != vocab_r_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::unordered_map<std::string, uint32_t> BPE::get_vocab() const {
    return vocab_;
}

size_t BPE::get_vocab_size() const {
    return vocab_.size();
}

std::unique_ptr<trainers::Trainer> BPE::get_trainer() {
    // We need to include the BPETrainer header to use it
    // For now, return nullptr as a placeholder
    return nullptr;
}

void BPE::save(const std::string& path, bool pretty) const {
    std::ofstream file(path);
    if (!file.is_open()) {
        throw std::runtime_error("Could not open file for writing: " + path);
    }
    
    // Simple JSON output for vocabulary
    file << "{\n";
    file << "  \"type\": \"BPE\",\n";
    file << "  \"vocab\": {\n";
    
    bool first = true;
    for (const auto& [token, id] : vocab_) {
        if (!first) file << ",\n";
        file << "    \"" << token << "\": " << id;
        first = false;
    }
    
    file << "\n  },\n";
    file << "  \"merges\": [\n";
    
    first = true;
    for (const auto& [first_token, second_token] : merges_) {
        if (!first) file << ",\n";
        file << "    \"" << first_token << " " << second_token << "\"";
        first = false;
    }
    
    file << "\n  ]\n";
    file << "}\n";
}

std::unique_ptr<Model> BPE::clone() const {
    return std::make_unique<BPE>(vocab_, merges_, dropout_, unk_token_,
                                continuing_subword_prefix_, end_of_word_suffix_, fuse_unk_);
}

std::vector<std::string> BPE::merge_word(const std::string& word) const {
    if (word.length() <= 1) {
        return {word};
    }
    
    // Initialize with character-level tokens
    std::vector<std::string> tokens;
    for (char c : word) {
        tokens.push_back(std::string(1, c));
    }
    
    // Apply merges
    while (tokens.size() > 1) {
        std::optional<uint32_t> best_rank;
        size_t best_pos = 0;
        
        // Find the best merge to apply
        for (size_t i = 0; i < tokens.size() - 1; ++i) {
            std::string pair = tokens[i] + " " + tokens[i + 1];
            auto rank = get_merge_rank(pair);
            
            if (rank && (!best_rank || *rank < *best_rank)) {
                best_rank = rank;
                best_pos = i;
            }
        }
        
        if (!best_rank) {
            break;  // No more merges possible
        }
        
        // Apply dropout if enabled
        if (dropout_ && *dropout_ > 0.0f) {
            static std::random_device rd;
            static std::mt19937 gen(rd());
            std::uniform_real_distribution<float> dis(0.0f, 1.0f);
            
            if (dis(gen) < *dropout_) {
                // Skip this merge due to dropout
                // Remove this pair from consideration by setting a high rank
                continue;
            }
        }
        
        // Perform the merge
        std::string merged = tokens[best_pos] + tokens[best_pos + 1];
        tokens[best_pos] = merged;
        tokens.erase(tokens.begin() + best_pos + 1);
    }
    
    return tokens;
}

std::vector<Token> BPE::word_to_tokens(const std::string& word,
                                     const std::vector<std::string>& subwords,
                                     size_t start_offset) const {
    std::vector<Token> tokens;
    size_t current_offset = start_offset;
    
    for (const auto& subword : subwords) {
        auto id_opt = token_to_id(subword);
        uint32_t id;
        
        if (id_opt) {
            id = *id_opt;
        } else if (unk_token_) {
            // Use unknown token ID
            auto unk_id_opt = token_to_id(*unk_token_);
            id = unk_id_opt ? *unk_id_opt : 0;
        } else {
            id = 0;  // Default unknown ID
        }
        
        size_t subword_length = subword.length();
        tokens.emplace_back(id, subword, std::make_pair(current_offset, current_offset + subword_length));
        current_offset += subword_length;
    }
    
    return tokens;
}

void BPE::build_merge_map() {
    merge_map_.clear();
    for (size_t i = 0; i < merges_.size(); ++i) {
        const auto& [first, second] = merges_[i];
        std::string pair = first + " " + second;
        merge_map_[pair] = static_cast<uint32_t>(i);
    }
}

std::optional<uint32_t> BPE::get_merge_rank(const std::string& pair) const {
    auto it = merge_map_.find(pair);
    return it != merge_map_.end() ? std::make_optional(it->second) : std::nullopt;
}

} // namespace models
} // namespace tokenizers
