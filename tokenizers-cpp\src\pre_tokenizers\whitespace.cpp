#include "tokenizers/pre_tokenizers/whitespace.hpp"
#include <cctype>
#include <sstream>

namespace tokenizers {
namespace pre_tokenizers {

void Whitespace::pre_tokenize(PreTokenizedString& pretokenized) {
    const auto& current_splits = pretokenized.get_splits();
    std::vector<std::string> new_splits;
    
    for (const auto& split : current_splits) {
        auto word_splits = split_on_whitespace(split);
        new_splits.insert(new_splits.end(), word_splits.begin(), word_splits.end());
    }
    
    pretokenized.set_splits(new_splits);
}

std::unique_ptr<PreTokenizer> Whitespace::clone() const {
    return std::make_unique<Whitespace>();
}

bool Whitespace::is_whitespace(char c) const {
    return std::isspace(static_cast<unsigned char>(c));
}

std::vector<std::string> Whitespace::split_on_whitespace(const std::string& text) const {
    std::vector<std::string> tokens;

    if (text.empty()) {
        return tokens;
    }

    size_t start = 0;

    while (start < text.length()) {
        // Skip leading whitespace
        while (start < text.length() && is_whitespace(text[start])) {
            start++;
        }

        // If we've reached the end, break
        if (start >= text.length()) {
            break;
        }

        // Find the end of the current token
        size_t end = start;
        while (end < text.length() && !is_whitespace(text[end])) {
            end++;
        }

        // Add the token
        tokens.push_back(text.substr(start, end - start));

        // Move to the next position
        start = end;
    }

    return tokens;
}

} // namespace pre_tokenizers
} // namespace tokenizers
