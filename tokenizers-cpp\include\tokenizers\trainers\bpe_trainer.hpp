#pragma once

#include "trainer.hpp"
#include "../added_token.hpp"
#include <unordered_map>
#include <unordered_set>
#include <string>
#include <vector>
#include <optional>
#include <cstdint>

namespace tokenizers {
namespace trainers {

/**
 * @brief Trainer for BPE (Byte Pair Encoding) models
 * 
 * The BPETrainer is responsible for training a BPE model from raw text data.
 * It learns the most frequent character pairs and creates merge rules.
 */
class BPETrainer : public Trainer {
private:
    /// Target vocabulary size
    size_t vocab_size_ = 30000;
    
    /// Minimum frequency for a pair to be considered for merging
    size_t min_frequency_ = 2;
    
    /// Special tokens to add to the vocabulary
    std::vector<AddedToken> special_tokens_;
    
    /// Whether to show progress during training
    bool show_progress_ = true;
    
    /// Continuing subword prefix (e.g., "##" for WordPiece-style)
    std::optional<std::string> continuing_subword_prefix_;
    
    /// End of word suffix (e.g., "</w>" for some BPE variants)
    std::optional<std::string> end_of_word_suffix_;
    
    /// Initial alphabet (characters seen in training data)
    std::unordered_set<std::string> initial_alphabet_;
    
    /// Word frequencies from training data
    std::unordered_map<std::string, size_t> word_freqs_;

public:
    /**
     * @brief Default constructor
     */
    BPETrainer() = default;
    
    /**
     * @brief Constructor with parameters
     * 
     * @param vocab_size Target vocabulary size
     * @param min_frequency Minimum frequency for merges
     * @param special_tokens Special tokens to add
     * @param show_progress Whether to show progress
     * @param continuing_subword_prefix Optional continuing subword prefix
     * @param end_of_word_suffix Optional end of word suffix
     */
    BPETrainer(size_t vocab_size,
               size_t min_frequency = 2,
               const std::vector<AddedToken>& special_tokens = {},
               bool show_progress = true,
               std::optional<std::string> continuing_subword_prefix = std::nullopt,
               std::optional<std::string> end_of_word_suffix = std::nullopt);
    
    /**
     * @brief Builder class for BPETrainer
     */
    class Builder {
    private:
        size_t vocab_size_ = 30000;
        size_t min_frequency_ = 2;
        std::vector<AddedToken> special_tokens_;
        bool show_progress_ = true;
        std::optional<std::string> continuing_subword_prefix_;
        std::optional<std::string> end_of_word_suffix_;
        
    public:
        Builder() = default;
        
        Builder& vocab_size(size_t vocab_size) { vocab_size_ = vocab_size; return *this; }
        Builder& min_frequency(size_t min_frequency) { min_frequency_ = min_frequency; return *this; }
        Builder& special_tokens(const std::vector<AddedToken>& special_tokens) { special_tokens_ = special_tokens; return *this; }
        Builder& show_progress(bool show_progress) { show_progress_ = show_progress; return *this; }
        Builder& continuing_subword_prefix(const std::string& prefix) { continuing_subword_prefix_ = prefix; return *this; }
        Builder& end_of_word_suffix(const std::string& suffix) { end_of_word_suffix_ = suffix; return *this; }
        
        BPETrainer build() {
            return BPETrainer(vocab_size_, min_frequency_, special_tokens_, show_progress_,
                             continuing_subword_prefix_, end_of_word_suffix_);
        }
    };
    
    /**
     * @brief Create a builder for BPETrainer
     * 
     * @return Builder A BPETrainer builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Trainer interface implementation
    std::vector<AddedToken> train(models::Model& model) override;
    void feed(const std::vector<std::string>& sequences,
             std::function<std::vector<std::string>(const std::string&)> process_func = nullptr) override;
    
    void set_vocab_size(size_t vocab_size) override { vocab_size_ = vocab_size; }
    size_t get_vocab_size() const override { return vocab_size_; }
    
    void set_min_frequency(size_t min_frequency) override { min_frequency_ = min_frequency; }
    size_t get_min_frequency() const override { return min_frequency_; }
    
    void set_special_tokens(const std::vector<AddedToken>& special_tokens) override { special_tokens_ = special_tokens; }
    const std::vector<AddedToken>& get_special_tokens() const override { return special_tokens_; }
    
    void set_show_progress(bool show_progress) override { show_progress_ = show_progress; }
    bool get_show_progress() const override { return show_progress_; }
    
    std::unique_ptr<Trainer> clone() const override;
    std::string get_type() const override { return "BPETrainer"; }
    
    // BPETrainer-specific methods
    
    /**
     * @brief Set the continuing subword prefix
     * 
     * @param prefix The prefix to use
     */
    void set_continuing_subword_prefix(const std::optional<std::string>& prefix) {
        continuing_subword_prefix_ = prefix;
    }
    
    /**
     * @brief Get the continuing subword prefix
     * 
     * @return std::optional<std::string> The prefix if set
     */
    std::optional<std::string> get_continuing_subword_prefix() const {
        return continuing_subword_prefix_;
    }
    
    /**
     * @brief Set the end of word suffix
     * 
     * @param suffix The suffix to use
     */
    void set_end_of_word_suffix(const std::optional<std::string>& suffix) {
        end_of_word_suffix_ = suffix;
    }
    
    /**
     * @brief Get the end of word suffix
     * 
     * @return std::optional<std::string> The suffix if set
     */
    std::optional<std::string> get_end_of_word_suffix() const {
        return end_of_word_suffix_;
    }

private:
    /**
     * @brief Compute initial alphabet from word frequencies
     */
    void compute_alphabet();
    
    /**
     * @brief Get all pairs and their frequencies from current word representations
     * 
     * @param word_freqs Current word frequencies
     * @return std::unordered_map<std::string, size_t> Pair frequencies
     */
    std::unordered_map<std::string, size_t> get_stats(
        const std::unordered_map<std::string, std::pair<std::vector<std::string>, size_t>>& word_freqs) const;
    
    /**
     * @brief Merge all occurrences of a pair in the word representations
     * 
     * @param word_freqs Current word frequencies (modified in place)
     * @param pair The pair to merge
     */
    void merge_vocab(std::unordered_map<std::string, std::pair<std::vector<std::string>, size_t>>& word_freqs,
                    const std::pair<std::string, std::string>& pair) const;
    
    /**
     * @brief Convert word frequency map to BPE format
     * 
     * @return std::unordered_map<std::string, std::pair<std::vector<std::string>, size_t>> BPE word format
     */
    std::unordered_map<std::string, std::pair<std::vector<std::string>, size_t>> prepare_words() const;
};

} // namespace trainers
} // namespace tokenizers
