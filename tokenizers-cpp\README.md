# Tokenizers C++

A C++ implementation of the Hugging Face Tokenizers library, providing fast and versatile tokenization for NLP applications.

## Features

- **Multiple Model Support**: BPE, WordPiece, WordLevel, and Unigram models
- **Flexible Pipeline**: Configurable normalization, pre-tokenization, and post-processing
- **High Performance**: Optimized C++ implementation with minimal dependencies
- **JSON Serialization**: Save and load tokenizer configurations
- **Training Support**: Train tokenizers from scratch on your data
- **Cross-Platform**: Works on Windows, Linux, and macOS

## Quick Start

### Building

```bash
mkdir build && cd build
cmake ..
make -j$(nproc)
```

### Basic Usage

```cpp
#include <tokenizers/tokenizer.hpp>
#include <tokenizers/models/bpe.hpp>

int main() {
    // Create a BPE tokenizer
    auto model = std::make_unique<tokenizers::models::BPE>();
    tokenizers::Tokenizer tokenizer(std::move(model));
    
    // Encode text
    auto encoding = tokenizer.encode("Hello, world!");
    
    // Get tokens and IDs
    auto tokens = encoding.get_tokens();
    auto ids = encoding.get_ids();
    
    // Decode back to text
    auto decoded = tokenizer.decode(ids);
    
    return 0;
}
```

### Training a Tokenizer

```cpp
#include <tokenizers/tokenizer.hpp>
#include <tokenizers/models/bpe.hpp>
#include <tokenizers/trainers/bpe_trainer.hpp>

int main() {
    // Create BPE model and tokenizer
    auto model = std::make_unique<tokenizers::models::BPE>();
    tokenizers::Tokenizer tokenizer(std::move(model));
    
    // Create trainer
    auto trainer = tokenizers::trainers::BPETrainer()
        .vocab_size(30000)
        .min_frequency(2)
        .special_tokens({"<unk>", "<s>", "</s>"});
    
    // Train from files
    std::vector<std::string> files = {"data/train.txt"};
    tokenizer.train_from_files(trainer, files);
    
    // Save the trained tokenizer
    tokenizer.save("tokenizer.json");
    
    return 0;
}
```

### Loading a Pre-trained Tokenizer

```cpp
#include <tokenizers/tokenizer.hpp>

int main() {
    // Load from file
    auto tokenizer = tokenizers::Tokenizer::from_file("tokenizer.json");
    
    // Use the tokenizer
    auto encoding = tokenizer->encode("Hello, world!");
    
    return 0;
}
```

## Architecture

The library follows a modular design with the following components:

- **Tokenizer**: Main class that orchestrates the tokenization pipeline
- **Models**: Core tokenization algorithms (BPE, WordPiece, etc.)
- **Normalizers**: Text preprocessing (Unicode normalization, case conversion, etc.)
- **PreTokenizers**: Initial text splitting (whitespace, punctuation, etc.)
- **PostProcessors**: Add special tokens and format output
- **Decoders**: Convert tokens back to readable text
- **Trainers**: Train models from raw text data

## Dependencies

- **C++17** or later
- **CMake 3.16** or later
- **nlohmann/json** (automatically downloaded if not found)
- **Google Test** (for testing, automatically downloaded if not found)

## Testing

Run the test suite:

```bash
cd build
ctest --verbose
```

## Examples

See the `examples/` directory for more detailed usage examples:

- `basic_usage.cpp`: Simple tokenization example
- `training_example.cpp`: Training a tokenizer from scratch
- `serialization_example.cpp`: Saving and loading tokenizers

## License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Acknowledgments

This C++ implementation is based on the original Rust tokenizers library by Hugging Face.
