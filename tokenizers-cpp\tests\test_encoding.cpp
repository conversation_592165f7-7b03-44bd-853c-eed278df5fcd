#include <gtest/gtest.h>
#include "tokenizers/encoding.hpp"
#include "tokenizers/token.hpp"

using namespace tokenizers;

class EncodingTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test tokens
        tokens_ = {
            Token(0, "hello", {0, 5}),
            Token(1, " ", {5, 6}),
            Token(2, "world", {6, 11})
        };
        
        encoding_ = Encoding::from_tokens(tokens_);
    }

    std::vector<Token> tokens_;
    Encoding encoding_;
};

TEST_F(EncodingTest, BasicProperties) {
    EXPECT_FALSE(encoding_.is_empty());
    EXPECT_EQ(encoding_.size(), 3);
    EXPECT_EQ(encoding_.length(), 3);
    
    const auto& ids = encoding_.get_ids();
    const auto& tokens = encoding_.get_tokens();
    const auto& offsets = encoding_.get_offsets();
    
    EXPECT_EQ(ids.size(), 3);
    EXPECT_EQ(tokens.size(), 3);
    EXPECT_EQ(offsets.size(), 3);
    
    EXPECT_EQ(ids[0], 0);
    EXPECT_EQ(ids[1], 1);
    EXPECT_EQ(ids[2], 2);
    
    EXPECT_EQ(tokens[0], "hello");
    EXPECT_EQ(tokens[1], " ");
    EXPECT_EQ(tokens[2], "world");
    
    EXPECT_EQ(offsets[0], std::make_pair(size_t(0), size_t(5)));
    EXPECT_EQ(offsets[1], std::make_pair(size_t(5), size_t(6)));
    EXPECT_EQ(offsets[2], std::make_pair(size_t(6), size_t(11)));
}

TEST_F(EncodingTest, EmptyEncoding) {
    Encoding empty;
    
    EXPECT_TRUE(empty.is_empty());
    EXPECT_EQ(empty.size(), 0);
    EXPECT_EQ(empty.length(), 0);
    EXPECT_EQ(empty.n_sequences(), 0);
}

TEST_F(EncodingTest, FromTokens) {
    std::vector<Token> test_tokens = {
        Token(10, "test", {0, 4}),
        Token(11, "token", {4, 9})
    };
    
    auto encoding = Encoding::from_tokens(test_tokens, 1);  // type_id = 1
    
    EXPECT_EQ(encoding.size(), 2);
    
    const auto& ids = encoding.get_ids();
    const auto& tokens = encoding.get_tokens();
    const auto& type_ids = encoding.get_type_ids();
    
    EXPECT_EQ(ids[0], 10);
    EXPECT_EQ(ids[1], 11);
    EXPECT_EQ(tokens[0], "test");
    EXPECT_EQ(tokens[1], "token");
    EXPECT_EQ(type_ids[0], 1);
    EXPECT_EQ(type_ids[1], 1);
}

TEST_F(EncodingTest, Padding) {
    // Test right padding
    encoding_.pad(5, 999, 0, "[PAD]", "right");
    
    EXPECT_EQ(encoding_.size(), 5);
    
    const auto& ids = encoding_.get_ids();
    const auto& tokens = encoding_.get_tokens();
    const auto& attention_mask = encoding_.get_attention_mask();
    
    // Original tokens should be unchanged
    EXPECT_EQ(ids[0], 0);
    EXPECT_EQ(ids[1], 1);
    EXPECT_EQ(ids[2], 2);
    
    // Padding tokens
    EXPECT_EQ(ids[3], 999);
    EXPECT_EQ(ids[4], 999);
    EXPECT_EQ(tokens[3], "[PAD]");
    EXPECT_EQ(tokens[4], "[PAD]");
    
    // Attention mask should be 1 for real tokens, 0 for padding
    EXPECT_EQ(attention_mask[0], 1);
    EXPECT_EQ(attention_mask[1], 1);
    EXPECT_EQ(attention_mask[2], 1);
    EXPECT_EQ(attention_mask[3], 0);
    EXPECT_EQ(attention_mask[4], 0);
}

TEST_F(EncodingTest, LeftPadding) {
    encoding_.pad(5, 999, 0, "[PAD]", "left");
    
    EXPECT_EQ(encoding_.size(), 5);
    
    const auto& ids = encoding_.get_ids();
    const auto& tokens = encoding_.get_tokens();
    
    // Padding tokens should be at the beginning
    EXPECT_EQ(ids[0], 999);
    EXPECT_EQ(ids[1], 999);
    EXPECT_EQ(tokens[0], "[PAD]");
    EXPECT_EQ(tokens[1], "[PAD]");
    
    // Original tokens should be at the end
    EXPECT_EQ(ids[2], 0);
    EXPECT_EQ(ids[3], 1);
    EXPECT_EQ(ids[4], 2);
}

TEST_F(EncodingTest, Truncation) {
    // Add more tokens first
    auto& ids = encoding_.get_ids();
    auto& tokens = encoding_.get_tokens();
    auto& offsets = encoding_.get_offsets();
    
    ids.insert(ids.end(), {3, 4, 5});
    tokens.insert(tokens.end(), {"!", " ", "test"});
    offsets.insert(offsets.end(), {{11, 12}, {12, 13}, {13, 17}});
    
    EXPECT_EQ(encoding_.size(), 6);
    
    // Test right truncation
    encoding_.truncate(4, 0, "right");
    
    EXPECT_EQ(encoding_.size(), 4);
    
    const auto& truncated_ids = encoding_.get_ids();
    EXPECT_EQ(truncated_ids[0], 0);
    EXPECT_EQ(truncated_ids[1], 1);
    EXPECT_EQ(truncated_ids[2], 2);
    EXPECT_EQ(truncated_ids[3], 3);
}

TEST_F(EncodingTest, TruncationWithStride) {
    // Add more tokens
    auto& ids = encoding_.get_ids();
    auto& tokens = encoding_.get_tokens();
    auto& offsets = encoding_.get_offsets();
    
    ids.insert(ids.end(), {3, 4, 5, 6});
    tokens.insert(tokens.end(), {"!", " ", "test", "ing"});
    offsets.insert(offsets.end(), {{11, 12}, {12, 13}, {13, 17}, {17, 20}});
    
    EXPECT_EQ(encoding_.size(), 7);
    
    // Truncate with stride
    encoding_.truncate(4, 2, "right");
    
    EXPECT_EQ(encoding_.size(), 4);
    
    // Check if overflow was created
    const auto& overflowing = encoding_.get_overflowing();
    EXPECT_GT(overflowing.size(), 0);
}

TEST_F(EncodingTest, CharToToken) {
    // Test character to token mapping
    auto token_idx = encoding_.char_to_token(0);  // First character of "hello"
    EXPECT_TRUE(token_idx.has_value());
    EXPECT_EQ(*token_idx, 0);
    
    token_idx = encoding_.char_to_token(3);  // Middle of "hello"
    EXPECT_TRUE(token_idx.has_value());
    EXPECT_EQ(*token_idx, 0);
    
    token_idx = encoding_.char_to_token(5);  // Space character
    EXPECT_TRUE(token_idx.has_value());
    EXPECT_EQ(*token_idx, 1);
    
    token_idx = encoding_.char_to_token(8);  // Middle of "world"
    EXPECT_TRUE(token_idx.has_value());
    EXPECT_EQ(*token_idx, 2);
    
    // Out of bounds
    token_idx = encoding_.char_to_token(100);
    EXPECT_FALSE(token_idx.has_value());
}

TEST_F(EncodingTest, TokenToChars) {
    auto char_span = encoding_.token_to_chars(0);  // "hello"
    EXPECT_TRUE(char_span.has_value());
    EXPECT_EQ(*char_span, std::make_pair(size_t(0), size_t(5)));
    
    char_span = encoding_.token_to_chars(1);  // " "
    EXPECT_TRUE(char_span.has_value());
    EXPECT_EQ(*char_span, std::make_pair(size_t(5), size_t(6)));
    
    char_span = encoding_.token_to_chars(2);  // "world"
    EXPECT_TRUE(char_span.has_value());
    EXPECT_EQ(*char_span, std::make_pair(size_t(6), size_t(11)));
    
    // Out of bounds
    char_span = encoding_.token_to_chars(10);
    EXPECT_FALSE(char_span.has_value());
}

TEST_F(EncodingTest, Clear) {
    EXPECT_FALSE(encoding_.is_empty());
    
    encoding_.clear();
    
    EXPECT_TRUE(encoding_.is_empty());
    EXPECT_EQ(encoding_.size(), 0);
    EXPECT_EQ(encoding_.get_ids().size(), 0);
    EXPECT_EQ(encoding_.get_tokens().size(), 0);
    EXPECT_EQ(encoding_.get_offsets().size(), 0);
}

TEST_F(EncodingTest, NSequences) {
    // Single sequence
    EXPECT_EQ(encoding_.n_sequences(), 1);
    
    // Empty encoding
    Encoding empty;
    EXPECT_EQ(empty.n_sequences(), 0);
}

// Test attention mask and special tokens mask
TEST_F(EncodingTest, Masks) {
    const auto& attention_mask = encoding_.get_attention_mask();
    const auto& special_tokens_mask = encoding_.get_special_tokens_mask();
    
    EXPECT_EQ(attention_mask.size(), 3);
    EXPECT_EQ(special_tokens_mask.size(), 3);
    
    // Default values
    for (size_t i = 0; i < 3; ++i) {
        EXPECT_EQ(attention_mask[i], 1);      // All tokens are attended to
        EXPECT_EQ(special_tokens_mask[i], 0); // No special tokens by default
    }
}

// Test type IDs
TEST_F(EncodingTest, TypeIds) {
    const auto& type_ids = encoding_.get_type_ids();
    
    EXPECT_EQ(type_ids.size(), 3);
    
    // Default type ID should be 0
    for (size_t i = 0; i < 3; ++i) {
        EXPECT_EQ(type_ids[i], 0);
    }
}
