#pragma once

#include "normalizer.hpp"
#include <vector>
#include <memory>

namespace tokenizers {

// Forward declaration
class NormalizedString;

namespace normalizers {

/**
 * @brief Sequence normalizer that applies multiple normalizers in order
 * 
 * This normalizer allows combining multiple normalization steps into a single pipeline.
 * Each normalizer in the sequence is applied to the result of the previous one.
 */
class SequenceNormalizer : public Normalizer {
private:
    /// The sequence of normalizers to apply
    std::vector<std::unique_ptr<Normalizer>> normalizers_;

public:
    /**
     * @brief Default constructor
     */
    SequenceNormalizer() = default;
    
    /**
     * @brief Construct with a vector of normalizers
     * 
     * @param normalizers The normalizers to apply in sequence
     */
    explicit SequenceNormalizer(std::vector<std::unique_ptr<Normalizer>> normalizers);
    
    /**
     * @brief Copy constructor
     */
    SequenceNormalizer(const SequenceNormalizer& other);
    
    /**
     * @brief Move constructor
     */
    SequenceNormalizer(SequenceNormalizer&& other) noexcept = default;
    
    /**
     * @brief Copy assignment operator
     */
    SequenceNormalizer& operator=(const SequenceNormalizer& other);
    
    /**
     * @brief Move assignment operator
     */
    SequenceNormalizer& operator=(SequenceNormalizer&& other) noexcept = default;
    
    /**
     * @brief Builder class for SequenceNormalizer
     */
    class Builder {
    private:
        std::vector<std::unique_ptr<Normalizer>> normalizers_;
        
    public:
        Builder() = default;
        
        /**
         * @brief Add a normalizer to the sequence
         * 
         * @param normalizer The normalizer to add
         * @return Builder& Reference to this builder for chaining
         */
        Builder& add(std::unique_ptr<Normalizer> normalizer) {
            normalizers_.push_back(std::move(normalizer));
            return *this;
        }
        
        /**
         * @brief Build the SequenceNormalizer
         * 
         * @return SequenceNormalizer The constructed normalizer
         */
        SequenceNormalizer build() {
            return SequenceNormalizer(std::move(normalizers_));
        }
    };
    
    /**
     * @brief Create a builder for SequenceNormalizer
     * 
     * @return Builder A SequenceNormalizer builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Normalizer interface implementation
    void normalize(NormalizedString& normalized) override;
    std::unique_ptr<Normalizer> clone() const override;
    std::string get_type() const override { return "SequenceNormalizer"; }
    
    // SequenceNormalizer-specific methods
    
    /**
     * @brief Add a normalizer to the end of the sequence
     * 
     * @param normalizer The normalizer to add
     */
    void add_normalizer(std::unique_ptr<Normalizer> normalizer);
    
    /**
     * @brief Get the number of normalizers in the sequence
     * 
     * @return size_t The number of normalizers
     */
    size_t size() const { return normalizers_.size(); }
    
    /**
     * @brief Check if the sequence is empty
     * 
     * @return bool True if the sequence is empty
     */
    bool empty() const { return normalizers_.empty(); }
    
    /**
     * @brief Clear all normalizers from the sequence
     */
    void clear() { normalizers_.clear(); }
};

} // namespace normalizers
} // namespace tokenizers
