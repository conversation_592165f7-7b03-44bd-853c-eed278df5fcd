#pragma once

#include <string>
#include <functional>

namespace tokenizers {

/**
 * @brief Represents a token added by the user on top of the existing model vocabulary
 * 
 * AddedToken can be configured to specify the behavior they should have in various situations:
 * - Whether they should only match single words
 * - Whether to include any whitespace on its left or right
 * - Whether they should be normalized
 * - Whether they are special tokens
 */
struct AddedToken {
    /// The content of the added token
    std::string content;
    
    /// Whether this token must be a single word or can break words
    bool single_word = false;
    
    /// Whether this token should strip whitespaces on its left
    bool lstrip = false;
    
    /// Whether this token should strip whitespaces on its right
    bool rstrip = false;
    
    /// Whether this token should be normalized
    bool normalized = true;
    
    /// Whether this token is special
    bool special = false;
    
    /**
     * @brief Default constructor
     */
    AddedToken() = default;
    
    /**
     * @brief Construct an AddedToken with content
     * 
     * @param content The token content
     */
    explicit AddedToken(const std::string& content) : content(content) {}
    
    /**
     * @brief Construct an AddedToken with content (move)
     * 
     * @param content The token content
     */
    explicit AddedToken(std::string&& content) : content(std::move(content)) {}
    
    /**
     * @brief Build this token from the given content, specifying if it is intended to be a special token
     * 
     * Special tokens are not normalized by default.
     * 
     * @param content The token content
     * @param special Whether this is a special token
     * @return AddedToken The constructed token
     */
    static AddedToken from(const std::string& content, bool special) {
        AddedToken token(content);
        token.special = special;
        token.normalized = !special;  // Special tokens are not normalized by default
        return token;
    }
    
    /**
     * @brief Build this token from the given content (move), specifying if it is intended to be a special token
     * 
     * @param content The token content
     * @param special Whether this is a special token
     * @return AddedToken The constructed token
     */
    static AddedToken from(std::string&& content, bool special) {
        AddedToken token(std::move(content));
        token.special = special;
        token.normalized = !special;  // Special tokens are not normalized by default
        return token;
    }
    
    /**
     * @brief Specify whether this token should only match on whole single words
     *
     * @param value Whether to match only single words
     * @return AddedToken& Reference to this token for chaining
     */
    AddedToken& set_single_word(bool value) {
        this->single_word = value;
        return *this;
    }

    /**
     * @brief Specify whether this token should include all whitespaces on its left
     *
     * @param value Whether to strip left whitespace
     * @return AddedToken& Reference to this token for chaining
     */
    AddedToken& set_lstrip(bool value) {
        this->lstrip = value;
        return *this;
    }

    /**
     * @brief Specify whether this token should include all whitespaces on its right
     *
     * @param value Whether to strip right whitespace
     * @return AddedToken& Reference to this token for chaining
     */
    AddedToken& set_rstrip(bool value) {
        this->rstrip = value;
        return *this;
    }

    /**
     * @brief Specify whether this token should be normalized
     *
     * @param value Whether to normalize this token
     * @return AddedToken& Reference to this token for chaining
     */
    AddedToken& set_normalized(bool value) {
        this->normalized = value;
        return *this;
    }

    /**
     * @brief Specify whether this token is special
     *
     * @param value Whether this is a special token
     * @return AddedToken& Reference to this token for chaining
     */
    AddedToken& set_special(bool value) {
        this->special = value;
        return *this;
    }
    
    /**
     * @brief Check if two AddedTokens are equal
     * 
     * @param other The other token to compare with
     * @return true if tokens are equal, false otherwise
     */
    bool operator==(const AddedToken& other) const {
        return content == other.content &&
               single_word == other.single_word &&
               lstrip == other.lstrip &&
               rstrip == other.rstrip &&
               normalized == other.normalized &&
               special == other.special;
    }
    
    /**
     * @brief Check if two AddedTokens are not equal
     * 
     * @param other The other token to compare with
     * @return true if tokens are not equal, false otherwise
     */
    bool operator!=(const AddedToken& other) const {
        return !(*this == other);
    }
};

} // namespace tokenizers

// Hash function for AddedToken to use in unordered containers
namespace std {
    template<>
    struct hash<tokenizers::AddedToken> {
        size_t operator()(const tokenizers::AddedToken& token) const {
            return hash<string>()(token.content);
        }
    };
}
