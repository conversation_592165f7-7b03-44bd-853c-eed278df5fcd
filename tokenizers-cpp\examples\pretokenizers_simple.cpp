#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include "tokenizers/pre_tokenizers/whitespace.hpp"
#include "tokenizers/pre_tokenizers/bert_pre_tokenizer.hpp"

using namespace tokenizers;

int main() {
    std::cout << "PreTokenizers Simple Example\n";
    std::cout << "============================\n\n";
    
    try {
        // Test strings
        std::vector<std::string> test_strings = {
            "Hello world!",
            "Don't split contractions",
            "Split on punctuation: yes, no?",
            "Multiple   spaces   here"
        };
        
        std::cout << "Example 1: Whitespace PreTokenizer\n";
        std::cout << "----------------------------------\n\n";
        
        auto whitespace_pretokenizer = std::make_unique<pre_tokenizers::Whitespace>();
        
        for (const auto& text : test_strings) {
            std::cout << "Input: \"" << text << "\"\n";
            
            auto splits = whitespace_pretokenizer->pre_tokenize_str(text);
            
            std::cout << "Splits: [";
            for (size_t i = 0; i < splits.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << splits[i] << "\"";
            }
            std::cout << "] (" << splits.size() << " tokens)\n\n";
        }
        
        std::cout << "Example 2: BERT PreTokenizer\n";
        std::cout << "----------------------------\n\n";
        
        auto bert_pretokenizer = std::make_unique<pre_tokenizers::BertPreTokenizer>();
        
        for (const auto& text : test_strings) {
            std::cout << "Input: \"" << text << "\"\n";
            
            auto splits = bert_pretokenizer->pre_tokenize_str(text);
            
            std::cout << "Splits: [";
            for (size_t i = 0; i < splits.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << splits[i] << "\"";
            }
            std::cout << "] (" << splits.size() << " tokens)\n\n";
        }
        
        std::cout << "Example 3: Comparison\n";
        std::cout << "--------------------\n\n";
        
        std::string comparison_text = "Hello, world! Don't split this.";
        std::cout << "Comparison text: \"" << comparison_text << "\"\n\n";
        
        // Whitespace
        auto ws_splits = whitespace_pretokenizer->pre_tokenize_str(comparison_text);
        std::cout << "Whitespace (" << ws_splits.size() << " tokens): [";
        for (size_t i = 0; i < ws_splits.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << "\"" << ws_splits[i] << "\"";
        }
        std::cout << "]\n\n";
        
        // BERT
        auto bert_splits = bert_pretokenizer->pre_tokenize_str(comparison_text);
        std::cout << "BERT (" << bert_splits.size() << " tokens): [";
        for (size_t i = 0; i < bert_splits.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << "\"" << bert_splits[i] << "\"";
        }
        std::cout << "]\n\n";
        
        std::cout << "PreTokenizers simple example completed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
