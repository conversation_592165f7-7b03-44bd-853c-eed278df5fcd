#pragma once

#include "normalizer.hpp"
#include <string>
#include <memory>

namespace tokenizers {

// Forward declaration
class NormalizedString;

namespace normalizers {

/**
 * @brief Strip normalizer that removes leading and trailing whitespace
 * 
 * This normalizer removes whitespace characters from the beginning and end of the text.
 * It can be configured to strip from the left, right, or both sides.
 */
class StripNormalizer : public Normalizer {
private:
    /// Whether to strip from the left
    bool left_;
    
    /// Whether to strip from the right
    bool right_;

public:
    /**
     * @brief Construct a new StripNormalizer
     * 
     * @param left Whether to strip from the left
     * @param right Whether to strip from the right
     */
    StripNormalizer(bool left = true, bool right = true);
    
    /**
     * @brief Builder class for StripNormalizer
     */
    class Builder {
    private:
        bool left_ = true;
        bool right_ = true;
        
    public:
        Builder() = default;
        
        Builder& left(bool left) { left_ = left; return *this; }
        Builder& right(bool right) { right_ = right; return *this; }
        
        StripNormalizer build() {
            return StripNormalizer(left_, right_);
        }
    };
    
    /**
     * @brief Create a builder for StripNormalizer
     * 
     * @return Builder A StripNormalizer builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Normalizer interface implementation
    void normalize(NormalizedString& normalized) override;
    std::unique_ptr<Normalizer> clone() const override;
    std::string get_type() const override { return "StripNormalizer"; }
    
    // StripNormalizer-specific methods
    
    /**
     * @brief Get whether left stripping is enabled
     * 
     * @return bool Whether left stripping is enabled
     */
    bool get_left() const { return left_; }
    
    /**
     * @brief Set whether to strip from the left
     * 
     * @param left Whether to strip from the left
     */
    void set_left(bool left) { left_ = left; }
    
    /**
     * @brief Get whether right stripping is enabled
     * 
     * @return bool Whether right stripping is enabled
     */
    bool get_right() const { return right_; }
    
    /**
     * @brief Set whether to strip from the right
     * 
     * @param right Whether to strip from the right
     */
    void set_right(bool right) { right_ = right; }

private:
    /**
     * @brief Check if a character is whitespace
     * 
     * @param c The character to check
     * @return bool True if the character is whitespace
     */
    bool is_whitespace(char c) const;
};

} // namespace normalizers
} // namespace tokenizers
