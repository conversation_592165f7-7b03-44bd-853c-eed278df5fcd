#pragma once

#include <string>
#include <vector>
#include <memory>

namespace tokenizers {

namespace decoders {

/**
 * @brief Base class for all decoders
 * 
 * A Decoder changes the raw tokens into their more readable form. It is responsible
 * for converting a sequence of tokens back into a human-readable string.
 */
class Decoder {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~Decoder() = default;
    
    /**
     * @brief Decode a sequence of tokens into a string
     * 
     * This method takes a vector of token strings and converts them back into
     * a readable text string.
     * 
     * @param tokens The tokens to decode
     * @return std::string The decoded string
     */
    virtual std::string decode(const std::vector<std::string>& tokens);
    
    /**
     * @brief Decode a sequence of tokens into a chain of strings
     * 
     * This method allows for more fine-grained control over the decoding process,
     * returning intermediate results that can be further processed.
     * 
     * @param tokens The tokens to decode
     * @return std::vector<std::string> The decoded string chain
     */
    virtual std::vector<std::string> decode_chain(const std::vector<std::string>& tokens) = 0;
    
    /**
     * @brief Clone this decoder
     * 
     * @return std::unique_ptr<Decoder> A copy of this decoder
     */
    virtual std::unique_ptr<Decoder> clone() const = 0;
    
    /**
     * @brief Get the decoder type name
     * 
     * @return std::string The decoder type
     */
    virtual std::string get_type() const = 0;
};

} // namespace decoders
} // namespace tokenizers
