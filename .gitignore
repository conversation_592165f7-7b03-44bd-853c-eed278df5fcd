# 编译输出
*.exe
*.dll
*.so
*.dylib
*.lib
*.a
*.obj
*.o
*.out

# CMake构建目录
build/
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
install_manifest.txt

# Visual Studio文件
.vs/
*.sln
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# IDE配置文件
.vscode/
.idea/
*.swp
*.swo

# 依赖管理
vcpkg_installed/
packages/
node_modules/

# 调试文件
*.pdb
*.idb
*.ilk

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.log
*.tmp
*.temp
temp/
tmp/

# Rust相关(因为项目中包含Rust代码)
target/
Cargo.lock
**/*.rs.bk