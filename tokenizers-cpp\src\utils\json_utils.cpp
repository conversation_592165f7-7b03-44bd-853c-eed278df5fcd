#include "tokenizers/utils/json_utils.hpp"
#include <algorithm>
#include <cctype>
#include <cstdio>

namespace tokenizers {
namespace utils {

std::string SimpleJson::serialize_vocab(const std::unordered_map<std::string, uint32_t>& map, bool pretty) {
    std::ostringstream oss;
    
    oss << "{";
    if (pretty) oss << "\n";
    
    bool first = true;
    for (const auto& [token, id] : map) {
        if (!first) {
            oss << ",";
            if (pretty) oss << "\n";
        }
        
        if (pretty) oss << "  ";
        oss << "\"" << escape_string(token) << "\": " << id;
        first = false;
    }
    
    if (pretty) oss << "\n";
    oss << "}";
    
    return oss.str();
}

std::string SimpleJson::serialize_merges(const std::vector<std::pair<std::string, std::string>>& pairs, bool pretty) {
    std::ostringstream oss;
    
    oss << "[";
    if (pretty) oss << "\n";
    
    for (size_t i = 0; i < pairs.size(); ++i) {
        if (i > 0) {
            oss << ",";
            if (pretty) oss << "\n";
        }
        
        if (pretty) oss << "  ";
        oss << "\"" << escape_string(pairs[i].first) << " " << escape_string(pairs[i].second) << "\"";
    }
    
    if (pretty) oss << "\n";
    oss << "]";
    
    return oss.str();
}

std::unordered_map<std::string, uint32_t> SimpleJson::parse_vocab(const std::string& json_str) {
    std::unordered_map<std::string, uint32_t> result;
    
    size_t pos = 0;
    skip_whitespace(json_str, pos);
    expect_char(json_str, pos, '{');
    
    skip_whitespace(json_str, pos);
    
    while (pos < json_str.length() && json_str[pos] != '}') {
        // Parse key
        std::string key = parse_string(json_str, pos);
        
        skip_whitespace(json_str, pos);
        expect_char(json_str, pos, ':');
        skip_whitespace(json_str, pos);
        
        // Parse value
        uint32_t value = parse_number(json_str, pos);
        
        result[key] = value;
        
        skip_whitespace(json_str, pos);
        if (pos < json_str.length() && json_str[pos] == ',') {
            pos++;
            skip_whitespace(json_str, pos);
        }
    }
    
    expect_char(json_str, pos, '}');
    
    return result;
}

std::vector<std::pair<std::string, std::string>> SimpleJson::parse_merges(const std::string& json_str) {
    std::vector<std::pair<std::string, std::string>> result;
    
    size_t pos = 0;
    skip_whitespace(json_str, pos);
    expect_char(json_str, pos, '[');
    
    skip_whitespace(json_str, pos);
    
    while (pos < json_str.length() && json_str[pos] != ']') {
        std::string merge_str = parse_string(json_str, pos);
        
        // Split the merge string on space
        size_t space_pos = merge_str.find(' ');
        if (space_pos != std::string::npos) {
            std::string first = merge_str.substr(0, space_pos);
            std::string second = merge_str.substr(space_pos + 1);
            result.emplace_back(first, second);
        }
        
        skip_whitespace(json_str, pos);
        if (pos < json_str.length() && json_str[pos] == ',') {
            pos++;
            skip_whitespace(json_str, pos);
        }
    }
    
    expect_char(json_str, pos, ']');
    
    return result;
}

std::string SimpleJson::escape_string(const std::string& str) {
    std::string result;
    result.reserve(str.length() * 2);
    
    for (char c : str) {
        switch (c) {
            case '"':  result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\b': result += "\\b"; break;
            case '\f': result += "\\f"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            default:
                if (c < 0x20) {
                    result += "\\u";
                    char buf[5];
                    snprintf(buf, sizeof(buf), "%04x", static_cast<unsigned char>(c));
                    result += buf;
                } else {
                    result += c;
                }
                break;
        }
    }
    
    return result;
}

std::string SimpleJson::unescape_string(const std::string& str) {
    std::string result;
    result.reserve(str.length());
    
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '\\' && i + 1 < str.length()) {
            switch (str[i + 1]) {
                case '"':  result += '"'; i++; break;
                case '\\': result += '\\'; i++; break;
                case 'b':  result += '\b'; i++; break;
                case 'f':  result += '\f'; i++; break;
                case 'n':  result += '\n'; i++; break;
                case 'r':  result += '\r'; i++; break;
                case 't':  result += '\t'; i++; break;
                case 'u':
                    if (i + 5 < str.length()) {
                        // Parse unicode escape (simplified)
                        std::string hex = str.substr(i + 2, 4);
                        unsigned int codepoint = std::stoul(hex, nullptr, 16);
                        if (codepoint < 0x80) {
                            result += static_cast<char>(codepoint);
                        } else {
                            result += '?';  // Simplified handling
                        }
                        i += 5;
                    } else {
                        result += str[i];
                    }
                    break;
                default:
                    result += str[i];
                    break;
            }
        } else {
            result += str[i];
        }
    }
    
    return result;
}

void SimpleJson::skip_whitespace(const std::string& str, size_t& pos) {
    while (pos < str.length() && std::isspace(str[pos])) {
        pos++;
    }
}

std::string SimpleJson::parse_string(const std::string& str, size_t& pos) {
    expect_char(str, pos, '"');
    
    std::string result;
    while (pos < str.length() && str[pos] != '"') {
        if (str[pos] == '\\' && pos + 1 < str.length()) {
            pos++;  // Skip the backslash
            switch (str[pos]) {
                case '"':  result += '"'; break;
                case '\\': result += '\\'; break;
                case 'b':  result += '\b'; break;
                case 'f':  result += '\f'; break;
                case 'n':  result += '\n'; break;
                case 'r':  result += '\r'; break;
                case 't':  result += '\t'; break;
                default:   result += str[pos]; break;
            }
        } else {
            result += str[pos];
        }
        pos++;
    }
    
    expect_char(str, pos, '"');
    
    return result;
}

uint32_t SimpleJson::parse_number(const std::string& str, size_t& pos) {
    size_t start = pos;
    
    while (pos < str.length() && std::isdigit(str[pos])) {
        pos++;
    }
    
    if (start == pos) {
        throw std::runtime_error("Expected number");
    }
    
    return std::stoul(str.substr(start, pos - start));
}

void SimpleJson::expect_char(const std::string& str, size_t& pos, char expected) {
    if (pos >= str.length() || str[pos] != expected) {
        throw std::runtime_error("Expected character: " + std::string(1, expected));
    }
    pos++;
}

} // namespace utils
} // namespace tokenizers
