cmake_minimum_required(VERSION 3.16)
project(tokenizers-cpp VERSION 0.21.2 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find required packages
find_package(PkgConfig QUIET)

# Option to build tests
option(BUILD_TESTS "Build tests" OFF)  # Disabled due to network issues
option(BUILD_EXAMPLES "Build examples" ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(TOKENIZERS_SOURCES
    src/tokenizer.cpp
    src/encoding.cpp
    src/token.cpp
    src/added_token.cpp
    src/added_vocabulary.cpp

    # Models
    src/models/model.cpp
    src/models/bpe.cpp
    src/models/wordpiece.cpp
    src/models/unigram.cpp

    # Normalizers
    src/normalizers/normalizer.cpp
    src/normalizers/bert_normalizer.cpp
    src/normalizers/strip_normalizer.cpp
    src/normalizers/sequence_normalizer.cpp

    # PreTokenizers
    src/pre_tokenizers/pre_tokenizer.cpp
    src/pre_tokenizers/whitespace.cpp
    src/pre_tokenizers/bert_pre_tokenizer.cpp

    # PostProcessors
    src/processors/post_processor.cpp
    src/post_processors/bert_processing.cpp

    # Decoders
    src/decoders/decoder.cpp
    src/decoders/bpe_decoder.cpp
    src/decoders/wordpiece_decoder.cpp

    # Trainers
    src/trainers/trainer.cpp
    src/trainers/bpe_trainer.cpp
    src/trainers/wordpiece_trainer.cpp

    # Utils
    src/utils/json_utils.cpp
    src/utils/pretokenized_string.cpp
)

# Header files
set(TOKENIZERS_HEADERS
    include/tokenizers/tokenizer.hpp
    include/tokenizers/encoding.hpp
    include/tokenizers/token.hpp
    include/tokenizers/added_token.hpp
    include/tokenizers/added_vocabulary.hpp

    # Models
    include/tokenizers/models/model.hpp
    include/tokenizers/models/bpe.hpp
    include/tokenizers/models/wordpiece.hpp
    include/tokenizers/models/unigram.hpp

    # Normalizers
    include/tokenizers/normalizers/normalizer.hpp
    include/tokenizers/normalizers/bert_normalizer.hpp
    include/tokenizers/normalizers/strip_normalizer.hpp
    include/tokenizers/normalizers/sequence_normalizer.hpp

    # PreTokenizers
    include/tokenizers/pre_tokenizers/pre_tokenizer.hpp
    include/tokenizers/pre_tokenizers/whitespace.hpp
    include/tokenizers/pre_tokenizers/bert_pre_tokenizer.hpp

    # PostProcessors
    include/tokenizers/processors/post_processor.hpp
    include/tokenizers/post_processors/bert_processing.hpp

    # Decoders
    include/tokenizers/decoders/decoder.hpp
    include/tokenizers/decoders/bpe_decoder.hpp
    include/tokenizers/decoders/wordpiece_decoder.hpp

    # Trainers
    include/tokenizers/trainers/trainer.hpp
    include/tokenizers/trainers/bpe_trainer.hpp
    include/tokenizers/trainers/wordpiece_trainer.hpp

    # Utils
    include/tokenizers/utils/json_utils.hpp
    include/tokenizers/utils/pretokenized_string.hpp
)

# Create the main library
add_library(tokenizers ${TOKENIZERS_SOURCES} ${TOKENIZERS_HEADERS})

# Set target properties
set_target_properties(tokenizers PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    PUBLIC_HEADER "${TOKENIZERS_HEADERS}"
)

# For now, we'll implement a simple JSON solution without external dependencies
# This can be replaced with nlohmann/json later when network access is available

# Platform-specific libraries
if(WIN32)
    target_link_libraries(tokenizers PRIVATE)
else()
    target_link_libraries(tokenizers PRIVATE pthread)
endif()

# Build tests (disabled for now due to network issues)
# if(BUILD_TESTS)
#     enable_testing()
#
#     # Find or fetch Google Test
#     find_package(GTest QUIET)
#     if(NOT GTest_FOUND)
#         include(FetchContent)
#         FetchContent_Declare(
#             googletest
#             URL https://github.com/google/googletest/archive/03597a01ee50ed33e9fd7188aa8c636f3b5e3c8e.zip
#         )
#         FetchContent_MakeAvailable(googletest)
#     endif()
#
#     # Test files
#     set(TEST_SOURCES
#         tests/test_tokenizer.cpp
#         tests/test_encoding.cpp
#         tests/test_models.cpp
#         tests/test_normalizers.cpp
#         tests/test_pre_tokenizers.cpp
#         tests/test_processors.cpp
#         tests/test_decoders.cpp
#         tests/test_training.cpp
#         tests/test_serialization.cpp
#     )
#
#     add_executable(tokenizers_tests ${TEST_SOURCES})
#     target_link_libraries(tokenizers_tests PRIVATE tokenizers gtest_main)
#
#     # Add tests to CTest
#     include(GoogleTest)
#     gtest_discover_tests(tokenizers_tests)
# endif()

# Build examples
if(BUILD_EXAMPLES)
    add_executable(basic_usage examples/basic_usage.cpp)
    target_link_libraries(basic_usage PRIVATE tokenizers)

    add_executable(training_example examples/training_example.cpp)
    target_link_libraries(training_example PRIVATE tokenizers)

    add_executable(compatibility_test examples/compatibility_test.cpp)
    target_link_libraries(compatibility_test PRIVATE tokenizers)

    add_executable(wordpiece_example examples/wordpiece_example.cpp)
    target_link_libraries(wordpiece_example PRIVATE tokenizers)

    add_executable(normalizers_example examples/normalizers_example.cpp)
    target_link_libraries(normalizers_example PRIVATE tokenizers)

    add_executable(pretokenizers_example examples/pretokenizers_example.cpp)
    target_link_libraries(pretokenizers_example PRIVATE tokenizers)

    add_executable(simple_pretokenizer_test examples/simple_pretokenizer_test.cpp)
    target_link_libraries(simple_pretokenizer_test PRIVATE tokenizers)

    add_executable(pretokenizers_simple examples/pretokenizers_simple.cpp)
    target_link_libraries(pretokenizers_simple PRIVATE tokenizers)

    add_executable(complete_pipeline examples/complete_pipeline.cpp)
    target_link_libraries(complete_pipeline PRIVATE tokenizers)
endif()

# Build tests
if(BUILD_TESTS)
    include(FetchContent)
    FetchContent_Declare(
        googletest
        URL https://github.com/google/googletest/archive/03597a01ee50f33f9142fd2db563d69b413611e6.zip
    )
    # For Windows: Prevent overriding the parent project's compiler/linker settings
    set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
    FetchContent_MakeAvailable(googletest)

    add_executable(tokenizers_tests
        tests/test_basic.cpp
        tests/test_added_tokens.cpp
        tests/test_offsets.cpp
        tests/test_training.cpp
        tests/test_unigram.cpp
        tests/test_serialization.cpp
    )

    target_link_libraries(tokenizers_tests
        PRIVATE
        tokenizers
        gtest_main
    )

    include(GoogleTest)
    gtest_discover_tests(tokenizers_tests)
endif()

# Installation
install(TARGETS tokenizers
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    PUBLIC_HEADER DESTINATION include/tokenizers
)

# Install headers with directory structure
install(DIRECTORY include/tokenizers
    DESTINATION include
    FILES_MATCHING PATTERN "*.hpp"
)

# Package configuration
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/tokenizers-config-version.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/tokenizers-config.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/tokenizers-config.cmake"
    INSTALL_DESTINATION lib/cmake/tokenizers
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/tokenizers-config.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/tokenizers-config-version.cmake"
    DESTINATION lib/cmake/tokenizers
)
