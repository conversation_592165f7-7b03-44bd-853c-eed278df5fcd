#pragma once

#include "trainer.hpp"
#include "../added_token.hpp"
#include <unordered_map>
#include <unordered_set>
#include <string>
#include <vector>
#include <optional>
#include <cstdint>

namespace tokenizers {
namespace trainers {

/**
 * @brief Trainer for WordPiece models
 * 
 * The WordPieceTrainer is responsible for training a WordPiece model from raw text data.
 * It uses a frequency-based approach to build a vocabulary of subword units.
 */
class WordPieceTrainer : public Trainer {
private:
    /// Target vocabulary size
    size_t vocab_size_ = 30000;
    
    /// Minimum frequency for a token to be included
    size_t min_frequency_ = 2;
    
    /// Special tokens to add to the vocabulary
    std::vector<AddedToken> special_tokens_;
    
    /// Whether to show progress during training
    bool show_progress_ = true;
    
    /// Continuing subword prefix (e.g., "##" for WordPiece)
    std::string continuing_subword_prefix_ = "##";
    
    /// Maximum input characters per word
    size_t max_input_chars_per_word_ = 100;
    
    /// Initial alphabet (characters seen in training data)
    std::unordered_set<std::string> initial_alphabet_;
    
    /// Word frequencies from training data
    std::unordered_map<std::string, size_t> word_freqs_;

public:
    /**
     * @brief Default constructor
     */
    WordPieceTrainer() = default;
    
    /**
     * @brief Constructor with parameters
     * 
     * @param vocab_size Target vocabulary size
     * @param min_frequency Minimum frequency for tokens
     * @param special_tokens Special tokens to add
     * @param show_progress Whether to show progress
     * @param continuing_subword_prefix Continuing subword prefix
     * @param max_input_chars_per_word Maximum input characters per word
     */
    WordPieceTrainer(size_t vocab_size,
                     size_t min_frequency = 2,
                     const std::vector<AddedToken>& special_tokens = {},
                     bool show_progress = true,
                     const std::string& continuing_subword_prefix = "##",
                     size_t max_input_chars_per_word = 100);
    
    /**
     * @brief Builder class for WordPieceTrainer
     */
    class Builder {
    private:
        size_t vocab_size_ = 30000;
        size_t min_frequency_ = 2;
        std::vector<AddedToken> special_tokens_;
        bool show_progress_ = true;
        std::string continuing_subword_prefix_ = "##";
        size_t max_input_chars_per_word_ = 100;
        
    public:
        Builder() = default;
        
        Builder& vocab_size(size_t vocab_size) { vocab_size_ = vocab_size; return *this; }
        Builder& min_frequency(size_t min_frequency) { min_frequency_ = min_frequency; return *this; }
        Builder& special_tokens(const std::vector<AddedToken>& special_tokens) { special_tokens_ = special_tokens; return *this; }
        Builder& show_progress(bool show_progress) { show_progress_ = show_progress; return *this; }
        Builder& continuing_subword_prefix(const std::string& prefix) { continuing_subword_prefix_ = prefix; return *this; }
        Builder& max_input_chars_per_word(size_t max_chars) { max_input_chars_per_word_ = max_chars; return *this; }
        
        WordPieceTrainer build() {
            return WordPieceTrainer(vocab_size_, min_frequency_, special_tokens_, show_progress_,
                                   continuing_subword_prefix_, max_input_chars_per_word_);
        }
    };
    
    /**
     * @brief Create a builder for WordPieceTrainer
     * 
     * @return Builder A WordPieceTrainer builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Trainer interface implementation
    std::vector<AddedToken> train(models::Model& model) override;
    void feed(const std::vector<std::string>& sequences,
             std::function<std::vector<std::string>(const std::string&)> process_func = nullptr) override;
    
    void set_vocab_size(size_t vocab_size) override { vocab_size_ = vocab_size; }
    size_t get_vocab_size() const override { return vocab_size_; }
    
    void set_min_frequency(size_t min_frequency) override { min_frequency_ = min_frequency; }
    size_t get_min_frequency() const override { return min_frequency_; }
    
    void set_special_tokens(const std::vector<AddedToken>& special_tokens) override { special_tokens_ = special_tokens; }
    const std::vector<AddedToken>& get_special_tokens() const override { return special_tokens_; }
    
    void set_show_progress(bool show_progress) override { show_progress_ = show_progress; }
    bool get_show_progress() const override { return show_progress_; }
    
    std::unique_ptr<Trainer> clone() const override;
    std::string get_type() const override { return "WordPieceTrainer"; }
    
    // WordPieceTrainer-specific methods
    
    /**
     * @brief Set the continuing subword prefix
     * 
     * @param prefix The prefix to use
     */
    void set_continuing_subword_prefix(const std::string& prefix) {
        continuing_subword_prefix_ = prefix;
    }
    
    /**
     * @brief Get the continuing subword prefix
     * 
     * @return const std::string& The prefix
     */
    const std::string& get_continuing_subword_prefix() const {
        return continuing_subword_prefix_;
    }
    
    /**
     * @brief Set the maximum input characters per word
     * 
     * @param max_chars The maximum characters
     */
    void set_max_input_chars_per_word(size_t max_chars) {
        max_input_chars_per_word_ = max_chars;
    }
    
    /**
     * @brief Get the maximum input characters per word
     * 
     * @return size_t The maximum characters
     */
    size_t get_max_input_chars_per_word() const {
        return max_input_chars_per_word_;
    }

private:
    /**
     * @brief Compute initial alphabet from word frequencies
     */
    void compute_alphabet();
    
    /**
     * @brief Generate all possible subwords for a word
     * 
     * @param word The word to generate subwords for
     * @return std::vector<std::string> All possible subwords
     */
    std::vector<std::string> generate_subwords(const std::string& word) const;
    
    /**
     * @brief Calculate the score for a subword based on frequency
     * 
     * @param subword The subword to score
     * @param subword_counts Frequency counts for all subwords
     * @return double The score for the subword
     */
    double calculate_score(const std::string& subword,
                          const std::unordered_map<std::string, size_t>& subword_counts) const;
};

} // namespace trainers
} // namespace tokenizers
