#include "tokenizers/trainers/bpe_trainer.hpp"
#include "tokenizers/models/bpe.hpp"
#include <iostream>
#include <algorithm>
#include <sstream>
#include <regex>

namespace tokenizers {
namespace trainers {

BPETrainer::BPETrainer(size_t vocab_size,
                       size_t min_frequency,
                       const std::vector<AddedToken>& special_tokens,
                       bool show_progress,
                       std::optional<std::string> continuing_subword_prefix,
                       std::optional<std::string> end_of_word_suffix)
    : vocab_size_(vocab_size)
    , min_frequency_(min_frequency)
    , special_tokens_(special_tokens)
    , show_progress_(show_progress)
    , continuing_subword_prefix_(continuing_subword_prefix)
    , end_of_word_suffix_(end_of_word_suffix) {
}

std::vector<AddedToken> BPETrainer::train(models::Model& model) {
    auto* bpe_model = dynamic_cast<models::BPE*>(&model);
    if (!bpe_model) {
        throw std::runtime_error("BPETrainer can only train BPE models");
    }
    
    if (show_progress_) {
        std::cout << "Training BPE with vocab_size=" << vocab_size_ 
                  << ", min_frequency=" << min_frequency_ << std::endl;
    }
    
    // Compute initial alphabet
    compute_alphabet();
    
    // Prepare words for BPE training
    auto word_freqs = prepare_words();
    
    // Build initial vocabulary from alphabet
    models::BPE::Vocab vocab;
    uint32_t next_id = 0;
    
    // Add special tokens first
    for (const auto& special_token : special_tokens_) {
        vocab[special_token.content] = next_id++;
    }
    
    // Add alphabet
    for (const auto& char_str : initial_alphabet_) {
        if (vocab.find(char_str) == vocab.end()) {
            vocab[char_str] = next_id++;
        }
    }
    
    // Learn merges
    models::BPE::Merges merges;
    
    while (vocab.size() < vocab_size_) {
        // Get pair statistics
        auto stats = get_stats(word_freqs);
        
        if (stats.empty()) {
            break;  // No more pairs to merge
        }
        
        // Find the most frequent pair
        auto best_pair = std::max_element(stats.begin(), stats.end(),
            [](const auto& a, const auto& b) {
                return a.second < b.second;
            });
        
        if (best_pair->second < min_frequency_) {
            break;  // Most frequent pair is below threshold
        }
        
        // Parse the pair
        std::istringstream iss(best_pair->first);
        std::string first, second;
        iss >> first >> second;
        
        if (show_progress_ && merges.size() % 1000 == 0) {
            std::cout << "Learned " << merges.size() << " merges..." << std::endl;
        }
        
        // Add the merge
        merges.emplace_back(first, second);
        
        // Add the merged token to vocabulary
        std::string merged = first + second;
        vocab[merged] = next_id++;
        
        // Apply the merge to all words
        merge_vocab(word_freqs, {first, second});
    }
    
    if (show_progress_) {
        std::cout << "Finished training. Learned " << merges.size() 
                  << " merges, final vocab size: " << vocab.size() << std::endl;
    }
    
    // Update the BPE model
    *bpe_model = models::BPE(std::move(vocab), std::move(merges));
    
    return special_tokens_;
}

void BPETrainer::feed(const std::vector<std::string>& sequences,
                     std::function<std::vector<std::string>(const std::string&)> process_func) {
    for (const auto& sequence : sequences) {
        if (process_func) {
            auto processed = process_func(sequence);
            for (const auto& word : processed) {
                word_freqs_[word]++;
            }
        } else {
            // Simple whitespace tokenization
            std::istringstream iss(sequence);
            std::string word;
            while (iss >> word) {
                // Remove punctuation and convert to lowercase for training
                std::regex punct_regex("[^a-zA-Z0-9]");
                word = std::regex_replace(word, punct_regex, "");
                if (!word.empty()) {
                    std::transform(word.begin(), word.end(), word.begin(), ::tolower);
                    word_freqs_[word]++;
                }
            }
        }
    }
}

std::unique_ptr<Trainer> BPETrainer::clone() const {
    return std::make_unique<BPETrainer>(vocab_size_, min_frequency_, special_tokens_,
                                       show_progress_, continuing_subword_prefix_, end_of_word_suffix_);
}

void BPETrainer::compute_alphabet() {
    initial_alphabet_.clear();
    
    for (const auto& [word, freq] : word_freqs_) {
        for (char c : word) {
            initial_alphabet_.insert(std::string(1, c));
        }
    }
}

std::unordered_map<std::string, size_t> BPETrainer::get_stats(
    const std::unordered_map<std::string, std::pair<std::vector<std::string>, size_t>>& word_freqs) const {
    
    std::unordered_map<std::string, size_t> pairs;
    
    for (const auto& [word, word_data] : word_freqs) {
        const auto& [symbols, freq] = word_data;
        
        for (size_t i = 0; i < symbols.size() - 1; ++i) {
            std::string pair = symbols[i] + " " + symbols[i + 1];
            pairs[pair] += freq;
        }
    }
    
    return pairs;
}

void BPETrainer::merge_vocab(
    std::unordered_map<std::string, std::pair<std::vector<std::string>, size_t>>& word_freqs,
    const std::pair<std::string, std::string>& pair) const {
    
    const auto& [first, second] = pair;
    
    for (auto& [word, word_data] : word_freqs) {
        auto& [symbols, freq] = word_data;
        
        std::vector<std::string> new_symbols;
        size_t i = 0;
        
        while (i < symbols.size()) {
            if (i < symbols.size() - 1 && symbols[i] == first && symbols[i + 1] == second) {
                // Merge the pair
                new_symbols.push_back(first + second);
                i += 2;
            } else {
                new_symbols.push_back(symbols[i]);
                i += 1;
            }
        }
        
        symbols = std::move(new_symbols);
    }
}

std::unordered_map<std::string, std::pair<std::vector<std::string>, size_t>> BPETrainer::prepare_words() const {
    std::unordered_map<std::string, std::pair<std::vector<std::string>, size_t>> result;
    
    for (const auto& [word, freq] : word_freqs_) {
        std::vector<std::string> symbols;
        
        // Split word into characters
        for (char c : word) {
            symbols.push_back(std::string(1, c));
        }
        
        // Add end-of-word suffix if specified
        if (end_of_word_suffix_ && !symbols.empty()) {
            symbols.back() += *end_of_word_suffix_;
        }
        
        result[word] = {std::move(symbols), freq};
    }
    
    return result;
}

} // namespace trainers
} // namespace tokenizers
