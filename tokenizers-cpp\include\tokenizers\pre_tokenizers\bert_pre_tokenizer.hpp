#pragma once

#include "pre_tokenizer.hpp"
#include <string>
#include <vector>
#include <memory>

namespace tokenizers {

namespace pre_tokenizers {

/**
 * @brief BERT-style pre-tokenizer
 * 
 * This pre-tokenizer implements the pre-tokenization strategy used in BERT:
 * - Split on whitespace
 * - Split on punctuation
 * - Handle special cases for contractions and hyphenated words
 */
class BertPreTokenizer : public PreTokenizer {
public:
    /**
     * @brief Default constructor
     */
    BertPreTokenizer() = default;
    
    // PreTokenizer interface implementation
    void pre_tokenize(PreTokenizedString& pretokenized) override;
    std::unique_ptr<PreTokenizer> clone() const override;
    std::string get_type() const override { return "BertPreTokenizer"; }

private:
    /**
     * @brief Check if a character is whitespace
     * 
     * @param c The character to check
     * @return bool True if the character is whitespace
     */
    bool is_whitespace(char c) const;
    
    /**
     * @brief Check if a character is punctuation
     * 
     * @param c The character to check
     * @return bool True if the character is punctuation
     */
    bool is_punctuation(char c) const;
    
    /**
     * @brief Check if a character is Chinese
     * 
     * @param c The character to check
     * @return bool True if the character is Chinese
     */
    bool is_chinese_char(unsigned char c) const;
    
    /**
     * @brief Split text using BERT's pre-tokenization rules
     * 
     * @param text The text to split
     * @return std::vector<std::string> The split tokens
     */
    std::vector<std::string> bert_split(const std::string& text) const;
    
    /**
     * @brief Split on whitespace
     * 
     * @param text The text to split
     * @return std::vector<std::string> The split tokens
     */
    std::vector<std::string> whitespace_split(const std::string& text) const;
    
    /**
     * @brief Split on punctuation
     * 
     * @param text The text to split
     * @return std::vector<std::string> The split tokens
     */
    std::vector<std::string> punctuation_split(const std::string& text) const;
};

} // namespace pre_tokenizers
} // namespace tokenizers
