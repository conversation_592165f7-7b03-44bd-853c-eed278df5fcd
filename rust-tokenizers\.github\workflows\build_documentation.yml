name: Build documentation

on:
  push:
    branches:
      - main
      - doc-builder*
      - v*-release
      - use_templates

jobs:
  build:
    uses: huggingface/doc-builder/.github/workflows/build_main_documentation.yml@main
    with:
      commit_sha: ${{ github.sha }}
      package: tokenizers
      path_to_docs: tokenizers/docs/source-doc-builder/
      package_path: tokenizers/bindings/python/
      install_rust: true
    secrets:
      hf_token: ${{ secrets.HF_DOC_BUILD_PUSH }}
