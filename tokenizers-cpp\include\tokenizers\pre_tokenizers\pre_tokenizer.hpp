#pragma once

#include "../utils/pretokenized_string.hpp"
#include <string>
#include <vector>
#include <memory>

namespace tokenizers {

namespace pre_tokenizers {

/**
 * @brief Base class for all pre-tokenizers
 * 
 * The PreTokenizer is in charge of doing the pre-segmentation step. It splits the given
 * string into multiple substrings, keeping track of the offsets of said substrings from
 * the original string. In some occasions, the PreTokenizer might need to modify the given
 * string to ensure we can entirely keep track of the offsets and the mapping with the
 * original string.
 */
class PreTokenizer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~PreTokenizer() = default;
    
    /**
     * @brief Pre-tokenize a PreTokenizedString in-place
     * 
     * This method splits the input into multiple substrings while maintaining
     * offset tracking for alignment with the original text.
     * 
     * @param pretokenized The pre-tokenized string to process
     */
    virtual void pre_tokenize(PreTokenizedString& pretokenized) = 0;
    
    /**
     * @brief Pre-tokenize a raw string and return the splits
     * 
     * This is a convenience method that creates a temporary PreTokenizedString,
     * applies pre-tokenization, and returns the splits.
     * 
     * @param input The input string to pre-tokenize
     * @return std::vector<std::string> The pre-tokenized splits
     */
    virtual std::vector<std::string> pre_tokenize_str(const std::string& input);
    
    /**
     * @brief Clone this pre-tokenizer
     * 
     * @return std::unique_ptr<PreTokenizer> A copy of this pre-tokenizer
     */
    virtual std::unique_ptr<PreTokenizer> clone() const = 0;
    
    /**
     * @brief Get the pre-tokenizer type name
     * 
     * @return std::string The pre-tokenizer type
     */
    virtual std::string get_type() const = 0;
};

} // namespace pre_tokenizers
} // namespace tokenizers
