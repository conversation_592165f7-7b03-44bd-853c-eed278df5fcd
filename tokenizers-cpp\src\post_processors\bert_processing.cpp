#include "tokenizers/post_processors/bert_processing.hpp"
#include "tokenizers/encoding.hpp"

namespace tokenizers {
namespace processors {

BertProcessing::BertProcessing(const AddedToken& sep_token, const AddedToken& cls_token)
    : sep_token_(sep_token), cls_token_(cls_token) {
}

size_t BertProcessing::added_tokens(bool is_pair) const {
    // [CLS] + [SEP] for single sequence
    // [CLS] + [SEP] + [SEP] for pair sequence
    return is_pair ? 3 : 2;
}

Encoding BertProcessing::process(const Encoding& encoding, const std::optional<Encoding>& pair, bool add_special_tokens) {
    if (!add_special_tokens) {
        // If not adding special tokens, just return a copy
        return encoding;
    }

    // Get current tokens and IDs
    const auto& tokens = encoding.get_tokens();
    const auto& ids = encoding.get_ids();
    const auto& offsets = encoding.get_offsets();
    const auto& attention_mask = encoding.get_attention_mask();
    const auto& special_tokens_mask = encoding.get_special_tokens_mask();

    // Create new vectors for the processed encoding
    std::vector<std::string> new_tokens;
    std::vector<uint32_t> new_ids;
    std::vector<std::pair<size_t, size_t>> new_offsets;
    std::vector<uint32_t> new_attention_mask;
    std::vector<uint32_t> new_special_tokens_mask;

    // Add [CLS] token at the beginning
    new_tokens.push_back(cls_token_.content);
    new_ids.push_back(0);  // We'll need to get the actual ID from the tokenizer
    new_offsets.push_back({0, 0});  // Special tokens have zero-length offsets
    new_attention_mask.push_back(1);
    new_special_tokens_mask.push_back(1);

    // Add original tokens
    new_tokens.insert(new_tokens.end(), tokens.begin(), tokens.end());
    new_ids.insert(new_ids.end(), ids.begin(), ids.end());
    new_offsets.insert(new_offsets.end(), offsets.begin(), offsets.end());
    new_attention_mask.insert(new_attention_mask.end(), attention_mask.begin(), attention_mask.end());
    new_special_tokens_mask.insert(new_special_tokens_mask.end(), special_tokens_mask.begin(), special_tokens_mask.end());

    // Add [SEP] token
    new_tokens.push_back(sep_token_.content);
    new_ids.push_back(0);  // We'll need to get the actual ID from the tokenizer
    new_offsets.push_back({0, 0});  // Special tokens have zero-length offsets
    new_attention_mask.push_back(1);
    new_special_tokens_mask.push_back(1);

    // If we have a pair encoding, add it
    if (pair) {
        const auto& pair_tokens = pair->get_tokens();
        const auto& pair_ids = pair->get_ids();
        const auto& pair_offsets = pair->get_offsets();
        const auto& pair_attention_mask = pair->get_attention_mask();
        const auto& pair_special_tokens_mask = pair->get_special_tokens_mask();

        // Add pair tokens
        new_tokens.insert(new_tokens.end(), pair_tokens.begin(), pair_tokens.end());
        new_ids.insert(new_ids.end(), pair_ids.begin(), pair_ids.end());
        new_offsets.insert(new_offsets.end(), pair_offsets.begin(), pair_offsets.end());
        new_attention_mask.insert(new_attention_mask.end(), pair_attention_mask.begin(), pair_attention_mask.end());
        new_special_tokens_mask.insert(new_special_tokens_mask.end(), pair_special_tokens_mask.begin(), pair_special_tokens_mask.end());

        // Add another [SEP] token
        new_tokens.push_back(sep_token_.content);
        new_ids.push_back(0);  // We'll need to get the actual ID from the tokenizer
        new_offsets.push_back({0, 0});  // Special tokens have zero-length offsets
        new_attention_mask.push_back(1);
        new_special_tokens_mask.push_back(1);
    }

    // Create type IDs (0 for first sentence, 1 for second sentence)
    std::vector<uint32_t> type_ids;
    size_t first_sentence_length = 1 + tokens.size() + 1;  // [CLS] + tokens + [SEP]

    // Type IDs for first sentence (including [CLS] and first [SEP])
    for (size_t i = 0; i < first_sentence_length; ++i) {
        type_ids.push_back(0);
    }

    // Type IDs for second sentence (if present)
    if (pair) {
        size_t second_sentence_length = pair->get_tokens().size() + 1;  // tokens + [SEP]
        for (size_t i = 0; i < second_sentence_length; ++i) {
            type_ids.push_back(1);
        }
    }

    // Create new encoding
    Encoding result(new_ids, type_ids, new_tokens, {}, new_offsets, new_special_tokens_mask, new_attention_mask);
    return result;
}

std::unique_ptr<PostProcessor> BertProcessing::clone() const {
    return std::make_unique<BertProcessing>(sep_token_, cls_token_);
}

} // namespace processors
} // namespace tokenizers
