#include "tokenizers/models/wordpiece.hpp"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <stdexcept>

namespace tokenizers {
namespace models {

WordPiece::WordPiece() 
    : unk_token_("[UNK]")
    , continuing_subword_prefix_("##")
    , max_input_chars_per_word_(100) {
}

WordPiece::WordPiece(Vocab vocab,
                     const std::string& unk_token,
                     const std::string& continuing_subword_prefix,
                     size_t max_input_chars_per_word)
    : vocab_(std::move(vocab))
    , unk_token_(unk_token)
    , continuing_subword_prefix_(continuing_subword_prefix)
    , max_input_chars_per_word_(max_input_chars_per_word) {
    
    // Build reverse vocabulary
    for (const auto& [token, id] : vocab_) {
        vocab_r_[id] = token;
    }
}

WordPiece WordPiece::from_file(const std::string& vocab_file,
                              const std::string& unk_token,
                              const std::string& continuing_subword_prefix,
                              size_t max_input_chars_per_word) {
    Vocab vocab;
    std::ifstream file(vocab_file);
    if (!file.is_open()) {
        throw std::runtime_error("Could not open vocabulary file: " + vocab_file);
    }
    
    std::string token;
    uint32_t id = 0;
    while (std::getline(file, token)) {
        if (!token.empty()) {
            vocab[token] = id++;
        }
    }
    
    return WordPiece(std::move(vocab), unk_token, continuing_subword_prefix, max_input_chars_per_word);
}

WordPiece WordPiece::Builder::build() {
    if (!vocab_) {
        throw std::runtime_error("Vocabulary must be provided");
    }
    
    return WordPiece(*vocab_, unk_token_, continuing_subword_prefix_, max_input_chars_per_word_);
}

std::vector<Token> WordPiece::tokenize(const std::string& sequence) {
    if (sequence.empty()) {
        return {};
    }
    
    std::vector<Token> tokens;
    
    // Split sequence into words (simple whitespace splitting for now)
    std::istringstream iss(sequence);
    std::string word;
    size_t offset = 0;
    
    while (iss >> word) {
        // Find the actual position of the word in the original sequence
        size_t word_start = sequence.find(word, offset);
        if (word_start == std::string::npos) {
            word_start = offset;
        }
        
        // Apply WordPiece to the word
        auto subwords = tokenize_word(word);
        auto word_tokens = word_to_tokens(word, subwords, word_start);
        
        tokens.insert(tokens.end(), word_tokens.begin(), word_tokens.end());
        
        offset = word_start + word.length();
    }
    
    return tokens;
}

std::optional<uint32_t> WordPiece::token_to_id(const std::string& token) const {
    auto it = vocab_.find(token);
    return it != vocab_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::optional<std::string> WordPiece::id_to_token(uint32_t id) const {
    auto it = vocab_r_.find(id);
    return it != vocab_r_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::unordered_map<std::string, uint32_t> WordPiece::get_vocab() const {
    return vocab_;
}

size_t WordPiece::get_vocab_size() const {
    return vocab_.size();
}

std::unique_ptr<trainers::Trainer> WordPiece::get_trainer() {
    // TODO: Implement WordPiece trainer
    return nullptr;
}

void WordPiece::save(const std::string& path, bool pretty) const {
    std::ofstream file(path);
    if (!file.is_open()) {
        throw std::runtime_error("Could not open file for writing: " + path);
    }
    
    // Simple JSON output for vocabulary
    file << "{\n";
    file << "  \"type\": \"WordPiece\",\n";
    file << "  \"unk_token\": \"" << unk_token_ << "\",\n";
    file << "  \"continuing_subword_prefix\": \"" << continuing_subword_prefix_ << "\",\n";
    file << "  \"max_input_chars_per_word\": " << max_input_chars_per_word_ << ",\n";
    file << "  \"vocab\": {\n";
    
    bool first = true;
    for (const auto& [token, id] : vocab_) {
        if (!first) file << ",\n";
        file << "    \"" << token << "\": " << id;
        first = false;
    }
    
    file << "\n  }\n";
    file << "}\n";
}

std::unique_ptr<Model> WordPiece::clone() const {
    return std::make_unique<WordPiece>(vocab_, unk_token_, continuing_subword_prefix_, max_input_chars_per_word_);
}

std::vector<std::string> WordPiece::tokenize_word(const std::string& word) const {
    if (word.length() > max_input_chars_per_word_) {
        return {unk_token_};
    }
    
    std::vector<std::string> output_tokens;
    
    size_t start = 0;
    while (start < word.length()) {
        size_t end = word.length();
        std::string cur_substr;
        bool found = false;
        
        // Greedy longest-match-first
        while (start < end) {
            std::string substr = word.substr(start, end - start);
            
            // Add continuing subword prefix if not at the beginning
            if (start > 0) {
                substr = continuing_subword_prefix_ + substr;
            }
            
            if (vocab_.find(substr) != vocab_.end()) {
                cur_substr = substr;
                found = true;
                break;
            }
            
            end--;
        }
        
        if (!found) {
            return {unk_token_};
        }
        
        output_tokens.push_back(cur_substr);
        start = end;
    }
    
    return output_tokens;
}

std::vector<Token> WordPiece::word_to_tokens(const std::string& word,
                                           const std::vector<std::string>& subwords,
                                           size_t start_offset) const {
    std::vector<Token> tokens;
    size_t current_offset = start_offset;
    
    for (const auto& subword : subwords) {
        auto id_opt = token_to_id(subword);
        uint32_t id;
        
        if (id_opt) {
            id = *id_opt;
        } else {
            // Use unknown token ID
            auto unk_id_opt = token_to_id(unk_token_);
            id = unk_id_opt ? *unk_id_opt : 0;
        }
        
        // Calculate the actual length in the original word
        std::string actual_subword = subword;
        if (actual_subword.length() >= continuing_subword_prefix_.length() &&
            actual_subword.substr(0, continuing_subword_prefix_.length()) == continuing_subword_prefix_) {
            actual_subword = actual_subword.substr(continuing_subword_prefix_.length());
        }
        
        size_t subword_length = actual_subword.length();
        tokens.emplace_back(id, subword, std::make_pair(current_offset, current_offset + subword_length));
        current_offset += subword_length;
    }
    
    return tokens;
}

} // namespace models
} // namespace tokenizers
