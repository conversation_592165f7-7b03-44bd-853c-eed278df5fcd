#pragma once

#include "added_token.hpp"
#include "token.hpp"
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <string>
#include <optional>
#include <memory>
#include <cstdint>

namespace tokenizers {

// Forward declarations
class NormalizedString;
class PreTokenizedString;

/**
 * @brief Manages additional tokens added on top of the base model vocabulary
 * 
 * The AddedVocabulary handles special tokens and user-added tokens that are not
 * part of the base model's vocabulary. It provides functionality for:
 * - Adding and managing special tokens
 * - Token matching and extraction
 * - Integration with the tokenization pipeline
 */
class AddedVocabulary {
private:
    /// Map from added token content to token ID
    std::unordered_map<std::string, uint32_t> added_tokens_map_;
    
    /// Map from token ID to AddedToken
    std::unordered_map<uint32_t, AddedToken> added_tokens_map_r_;
    
    /// Set of special token IDs
    std::unordered_set<uint32_t> special_tokens_;
    
    /// Next available token ID
    uint32_t next_id_;
    
    /// Compiled pattern matcher for efficient token matching
    struct PatternMatcher;
    std::unique_ptr<PatternMatcher> matcher_;
    
    /// Whether the matcher needs to be rebuilt
    bool matcher_dirty_;

public:
    /**
     * @brief Construct a new AddedVocabulary
     */
    AddedVocabulary();
    
    /**
     * @brief Destructor
     */
    ~AddedVocabulary();
    
    /**
     * @brief Copy constructor
     */
    AddedVocabulary(const AddedVocabulary& other);
    
    /**
     * @brief Move constructor
     */
    AddedVocabulary(AddedVocabulary&& other) noexcept;
    
    /**
     * @brief Copy assignment operator
     */
    AddedVocabulary& operator=(const AddedVocabulary& other);
    
    /**
     * @brief Move assignment operator
     */
    AddedVocabulary& operator=(AddedVocabulary&& other) noexcept;
    
    /**
     * @brief Add a single token to the vocabulary
     * 
     * @param token The token to add
     * @return uint32_t The assigned token ID
     */
    uint32_t add_token(const AddedToken& token);
    
    /**
     * @brief Add multiple tokens to the vocabulary
     * 
     * @param tokens Vector of tokens to add
     * @return std::vector<uint32_t> Vector of assigned token IDs
     */
    std::vector<uint32_t> add_tokens(const std::vector<AddedToken>& tokens);
    
    /**
     * @brief Add a simple string token
     * 
     * @param token The token string
     * @param special Whether this is a special token
     * @return uint32_t The assigned token ID
     */
    uint32_t add_token(const std::string& token, bool special = false);
    
    /**
     * @brief Add multiple string tokens
     * 
     * @param tokens Vector of token strings
     * @param special Whether these are special tokens
     * @return std::vector<uint32_t> Vector of assigned token IDs
     */
    std::vector<uint32_t> add_tokens(const std::vector<std::string>& tokens, bool special = false);
    
    /**
     * @brief Get the token ID for a given token string
     * 
     * @param token The token string
     * @return std::optional<uint32_t> The token ID if found
     */
    std::optional<uint32_t> token_to_id(const std::string& token) const;
    
    /**
     * @brief Get the token string for a given token ID
     * 
     * @param id The token ID
     * @return std::optional<std::string> The token string if found
     */
    std::optional<std::string> id_to_token(uint32_t id) const;
    
    /**
     * @brief Get the AddedToken for a given token ID
     * 
     * @param id The token ID
     * @return std::optional<AddedToken> The AddedToken if found
     */
    std::optional<AddedToken> get_added_token(uint32_t id) const;
    
    /**
     * @brief Check if a token ID corresponds to a special token
     * 
     * @param id The token ID
     * @return true if the token is special, false otherwise
     */
    bool is_special_token(uint32_t id) const;
    
    /**
     * @brief Get the number of added tokens
     * 
     * @return size_t The number of added tokens
     */
    size_t size() const { return added_tokens_map_.size(); }
    
    /**
     * @brief Check if the vocabulary is empty
     * 
     * @return true if empty, false otherwise
     */
    bool empty() const { return added_tokens_map_.empty(); }
    
    /**
     * @brief Clear all added tokens
     */
    void clear();
    
    /**
     * @brief Get all added tokens as a map
     * 
     * @return const std::unordered_map<std::string, uint32_t>& Map of token strings to IDs
     */
    const std::unordered_map<std::string, uint32_t>& get_vocab() const { return added_tokens_map_; }
    
    /**
     * @brief Extract added tokens from a normalized string
     * 
     * This method finds and extracts added tokens from the input string,
     * modifying the normalized string to mark the extracted tokens.
     * 
     * @param normalized The normalized string to process
     * @return std::vector<Token> Vector of extracted tokens
     */
    std::vector<Token> extract_and_normalize(NormalizedString& normalized) const;
    
    /**
     * @brief Split a pre-tokenized string on added tokens
     * 
     * @param pretokenized The pre-tokenized string to process
     */
    void split_pretokenized(PreTokenizedString& pretokenized) const;

private:
    /**
     * @brief Rebuild the pattern matcher if needed
     */
    void rebuild_matcher() const;
    
    /**
     * @brief Mark the matcher as dirty (needs rebuilding)
     */
    void mark_matcher_dirty() { matcher_dirty_ = true; }
};

} // namespace tokenizers
