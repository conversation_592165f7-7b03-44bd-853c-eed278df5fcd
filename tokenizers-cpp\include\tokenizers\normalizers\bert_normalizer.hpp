#pragma once

#include "normalizer.hpp"
#include <string>
#include <memory>

namespace tokenizers {

// Forward declaration
class NormalizedString;

namespace normalizers {

/**
 * @brief BERT-style text normalizer
 * 
 * This normalizer performs the text preprocessing steps commonly used in BERT:
 * - Clean text (remove control characters, normalize whitespace)
 * - Handle Chinese characters
 * - Strip accents (optional)
 * - Convert to lowercase (optional)
 */
class BertNormalizer : public Normalizer {
private:
    /// Whether to clean the text
    bool clean_text_;
    
    /// Whether to handle Chinese characters
    bool handle_chinese_chars_;
    
    /// Whether to strip accents
    bool strip_accents_;
    
    /// Whether to convert to lowercase
    bool lowercase_;

public:
    /**
     * @brief Construct a new BertNormalizer
     * 
     * @param clean_text Whether to clean the text
     * @param handle_chinese_chars Whether to handle Chinese characters
     * @param strip_accents Whether to strip accents
     * @param lowercase Whether to convert to lowercase
     */
    BertNormalizer(bool clean_text = true,
                   bool handle_chinese_chars = true,
                   bool strip_accents = true,
                   bool lowercase = true);
    
    /**
     * @brief Builder class for BertNormalizer
     */
    class Builder {
    private:
        bool clean_text_ = true;
        bool handle_chinese_chars_ = true;
        bool strip_accents_ = true;
        bool lowercase_ = true;
        
    public:
        Builder() = default;
        
        Builder& clean_text(bool clean_text) { clean_text_ = clean_text; return *this; }
        Builder& handle_chinese_chars(bool handle_chinese_chars) { handle_chinese_chars_ = handle_chinese_chars; return *this; }
        Builder& strip_accents(bool strip_accents) { strip_accents_ = strip_accents; return *this; }
        Builder& lowercase(bool lowercase) { lowercase_ = lowercase; return *this; }
        
        BertNormalizer build() {
            return BertNormalizer(clean_text_, handle_chinese_chars_, strip_accents_, lowercase_);
        }
    };
    
    /**
     * @brief Create a builder for BertNormalizer
     * 
     * @return Builder A BertNormalizer builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Normalizer interface implementation
    void normalize(NormalizedString& normalized) override;
    std::unique_ptr<Normalizer> clone() const override;
    std::string get_type() const override { return "BertNormalizer"; }
    
    // BertNormalizer-specific methods
    
    /**
     * @brief Get whether text cleaning is enabled
     * 
     * @return bool Whether text cleaning is enabled
     */
    bool get_clean_text() const { return clean_text_; }
    
    /**
     * @brief Set whether to clean text
     * 
     * @param clean_text Whether to clean text
     */
    void set_clean_text(bool clean_text) { clean_text_ = clean_text; }
    
    /**
     * @brief Get whether Chinese character handling is enabled
     * 
     * @return bool Whether Chinese character handling is enabled
     */
    bool get_handle_chinese_chars() const { return handle_chinese_chars_; }
    
    /**
     * @brief Set whether to handle Chinese characters
     * 
     * @param handle_chinese_chars Whether to handle Chinese characters
     */
    void set_handle_chinese_chars(bool handle_chinese_chars) { handle_chinese_chars_ = handle_chinese_chars; }
    
    /**
     * @brief Get whether accent stripping is enabled
     * 
     * @return bool Whether accent stripping is enabled
     */
    bool get_strip_accents() const { return strip_accents_; }
    
    /**
     * @brief Set whether to strip accents
     * 
     * @param strip_accents Whether to strip accents
     */
    void set_strip_accents(bool strip_accents) { strip_accents_ = strip_accents; }
    
    /**
     * @brief Get whether lowercasing is enabled
     * 
     * @return bool Whether lowercasing is enabled
     */
    bool get_lowercase() const { return lowercase_; }
    
    /**
     * @brief Set whether to convert to lowercase
     * 
     * @param lowercase Whether to convert to lowercase
     */
    void set_lowercase(bool lowercase) { lowercase_ = lowercase; }

private:
    /**
     * @brief Clean text by removing control characters and normalizing whitespace
     * 
     * @param text The text to clean
     * @return std::string The cleaned text
     */
    std::string clean_text(const std::string& text) const;
    
    /**
     * @brief Handle Chinese characters by adding spaces around them
     * 
     * @param text The text to process
     * @return std::string The processed text
     */
    std::string handle_chinese_chars(const std::string& text) const;
    
    /**
     * @brief Strip accents from characters
     * 
     * @param text The text to process
     * @return std::string The processed text
     */
    std::string strip_accents(const std::string& text) const;
    
    /**
     * @brief Convert text to lowercase
     * 
     * @param text The text to convert
     * @return std::string The lowercase text
     */
    std::string to_lowercase(const std::string& text) const;
    
    /**
     * @brief Check if a character is a control character
     * 
     * @param c The character to check
     * @return bool True if the character is a control character
     */
    bool is_control(char c) const;
    
    /**
     * @brief Check if a character is whitespace
     * 
     * @param c The character to check
     * @return bool True if the character is whitespace
     */
    bool is_whitespace(char c) const;
    
    /**
     * @brief Check if a character is Chinese
     * 
     * @param c The character to check (as unsigned char for extended ASCII)
     * @return bool True if the character is Chinese
     */
    bool is_chinese_char(unsigned char c) const;
};

} // namespace normalizers
} // namespace tokenizers
