#pragma once

#include "../encoding.hpp"
#include <memory>
#include <optional>

namespace tokenizers {

namespace processors {

/**
 * @brief Base class for all post-processors
 * 
 * A PostProcessor has the responsibility to post-process an encoded output of the Tokenizer.
 * It adds any special tokens that a language model would require, handles sequence pairs,
 * and manages type IDs.
 */
class PostProcessor {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~PostProcessor() = default;
    
    /**
     * @brief Returns the number of tokens that will be added during the processing step
     * 
     * @param is_pair Whether we are processing a pair of sequences
     * @return size_t The number of tokens that will be added
     */
    virtual size_t added_tokens(bool is_pair) const = 0;
    
    /**
     * @brief Process the encoding(s) and return a new merged one
     * 
     * @param encoding The main encoding to process
     * @param pair Optional second encoding for sequence pairs
     * @param add_special_tokens Whether to add special tokens
     * @return Encoding The processed encoding
     */
    virtual Encoding process(const Encoding& encoding,
                           const std::optional<Encoding>& pair = std::nullopt,
                           bool add_special_tokens = true) = 0;
    
    /**
     * @brief Clone this post-processor
     * 
     * @return std::unique_ptr<PostProcessor> A copy of this post-processor
     */
    virtual std::unique_ptr<PostProcessor> clone() const = 0;
    
    /**
     * @brief Get the post-processor type name
     * 
     * @return std::string The post-processor type
     */
    virtual std::string get_type() const = 0;
};

} // namespace processors
} // namespace tokenizers
