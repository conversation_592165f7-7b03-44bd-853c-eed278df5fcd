#pragma once

#include "model.hpp"
#include "../trainers/trainer.hpp"
#include <unordered_map>
#include <string>
#include <vector>
#include <optional>
#include <cstdint>

namespace tokenizers {
namespace models {

/**
 * @brief A Unigram model
 * 
 * Unigram is a subword tokenization algorithm that uses a probabilistic approach
 * to find the most likely segmentation of a word into subwords. It's commonly used
 * in SentencePiece and other modern tokenizers.
 */
class Unigram : public Model {
public:
    /// Type alias for vocabulary (token -> (ID, score) mapping)
    using Vocab = std::unordered_map<std::string, std::pair<uint32_t, double>>;
    
    /// Type alias for reverse vocabulary (ID -> token mapping)
    using VocabR = std::unordered_map<uint32_t, std::string>;

private:
    /// The vocabulary with scores
    Vocab vocab_;
    
    /// Reversed vocabulary, to rebuild sentences
    VocabR vocab_r_;
    
    /// The unknown token to be used when we encounter an unknown character
    std::string unk_token_;
    
    /// Minimum score for a token to be considered
    double min_score_;

public:
    /**
     * @brief Default constructor
     */
    Unigram();
    
    /**
     * @brief Construct Unigram with vocabulary
     * 
     * @param vocab The vocabulary mapping with scores
     * @param unk_token The unknown token
     * @param min_score Minimum score for tokens
     */
    Unigram(Vocab vocab,
            const std::string& unk_token = "<unk>",
            double min_score = 0.0);
    
    /**
     * @brief Create Unigram from vocabulary file
     * 
     * @param vocab_file Path to vocabulary file (JSON format)
     * @param unk_token The unknown token
     * @return Unigram The constructed Unigram model
     */
    static Unigram from_file(const std::string& vocab_file,
                            const std::string& unk_token = "<unk>");
    
    /**
     * @brief Builder class for Unigram
     */
    class Builder {
    private:
        std::optional<Vocab> vocab_;
        std::string unk_token_ = "<unk>";
        double min_score_ = 0.0;
        
    public:
        Builder() = default;
        
        Builder& vocab(const Vocab& vocab) { vocab_ = vocab; return *this; }
        Builder& unk_token(const std::string& unk_token) { unk_token_ = unk_token; return *this; }
        Builder& min_score(double min_score) { min_score_ = min_score; return *this; }
        
        Unigram build();
    };
    
    /**
     * @brief Create a builder for Unigram
     * 
     * @return Builder A Unigram builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Model interface implementation
    std::vector<Token> tokenize(const std::string& sequence) override;
    std::optional<uint32_t> token_to_id(const std::string& token) const override;
    std::optional<std::string> id_to_token(uint32_t id) const override;
    std::unordered_map<std::string, uint32_t> get_vocab() const override;
    size_t get_vocab_size() const override;
    std::unique_ptr<trainers::Trainer> get_trainer() override;
    void save(const std::string& path, bool pretty = false) const override;
    std::unique_ptr<Model> clone() const override;
    std::string get_type() const override { return "Unigram"; }
    
    // Unigram-specific methods
    
    /**
     * @brief Get the unknown token
     * 
     * @return const std::string& The unknown token
     */
    const std::string& get_unk_token() const { return unk_token_; }
    
    /**
     * @brief Set the unknown token
     * 
     * @param unk_token The unknown token
     */
    void set_unk_token(const std::string& unk_token) { unk_token_ = unk_token; }
    
    /**
     * @brief Get the minimum score
     * 
     * @return double The minimum score
     */
    double get_min_score() const { return min_score_; }
    
    /**
     * @brief Set the minimum score
     * 
     * @param min_score The minimum score
     */
    void set_min_score(double min_score) { min_score_ = min_score; }
    
    /**
     * @brief Get the vocabulary with scores
     * 
     * @return const Vocab& The vocabulary with scores
     */
    const Vocab& get_vocab_with_scores() const { return vocab_; }

private:
    /**
     * @brief Find the best segmentation using Viterbi algorithm
     * 
     * @param text The text to segment
     * @return std::vector<std::string> The best segmentation
     */
    std::vector<std::string> viterbi_search(const std::string& text) const;
    
    /**
     * @brief Get the score for a token
     * 
     * @param token The token to score
     * @return double The score (log probability)
     */
    double get_score(const std::string& token) const;
    
    /**
     * @brief Convert segmentation to tokens with proper IDs and offsets
     *
     * @param text The original text
     * @param segmentation The segmentation
     * @return std::vector<Token> The tokens with IDs and offsets
     */
    std::vector<Token> segmentation_to_tokens(const std::string& text,
                                             const std::vector<std::string>& segmentation) const;

    /**
     * @brief Check if a character is valid for tokenization
     *
     * @param c The character to check
     * @return bool True if the character is valid
     */
    bool is_valid_char(char c) const;
};

} // namespace models
} // namespace tokenizers
