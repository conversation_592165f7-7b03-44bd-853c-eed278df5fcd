#pragma once

#include "../processors/post_processor.hpp"
#include "../added_token.hpp"
#include <string>
#include <memory>
#include <optional>

namespace tokenizers {
namespace processors {

/**
 * @brief BERT-style post-processor
 * 
 * This post-processor adds special tokens for BERT-style models:
 * - Adds [CLS] token at the beginning
 * - Adds [SEP] token at the end
 * - For sentence pairs, adds [SEP] between sentences
 */
class BertProcessing : public PostProcessor {
private:
    /// The [SEP] token
    AddedToken sep_token_;
    
    /// The [CLS] token
    AddedToken cls_token_;

public:
    /**
     * @brief Construct a new BertProcessing
     * 
     * @param sep_token The separator token (default: "[SEP]")
     * @param cls_token The classification token (default: "[CLS]")
     */
    BertProcessing(const AddedToken& sep_token = AddedToken::from("[SEP]", true),
                   const AddedToken& cls_token = AddedToken::from("[CLS]", true));
    
    /**
     * @brief Builder class for BertProcessing
     */
    class Builder {
    private:
        AddedToken sep_token_ = AddedToken::from("[SEP]", true);
        AddedToken cls_token_ = AddedToken::from("[CLS]", true);
        
    public:
        Builder() = default;
        
        Builder& sep_token(const AddedToken& sep_token) { sep_token_ = sep_token; return *this; }
        Builder& cls_token(const AddedToken& cls_token) { cls_token_ = cls_token; return *this; }
        
        BertProcessing build() {
            return BertProcessing(sep_token_, cls_token_);
        }
    };
    
    /**
     * @brief Create a builder for BertProcessing
     * 
     * @return Builder A BertProcessing builder instance
     */
    static Builder builder() { return Builder(); }
    
    // PostProcessor interface implementation
    size_t added_tokens(bool is_pair) const override;
    Encoding process(const Encoding& encoding, const std::optional<Encoding>& pair = std::nullopt, bool add_special_tokens = true) override;
    std::unique_ptr<PostProcessor> clone() const override;
    std::string get_type() const override { return "BertProcessing"; }
    
    // BertProcessing-specific methods
    
    /**
     * @brief Get the separator token
     * 
     * @return const AddedToken& The separator token
     */
    const AddedToken& get_sep_token() const { return sep_token_; }
    
    /**
     * @brief Set the separator token
     * 
     * @param sep_token The separator token
     */
    void set_sep_token(const AddedToken& sep_token) { sep_token_ = sep_token; }
    
    /**
     * @brief Get the classification token
     * 
     * @return const AddedToken& The classification token
     */
    const AddedToken& get_cls_token() const { return cls_token_; }
    
    /**
     * @brief Set the classification token
     * 
     * @param cls_token The classification token
     */
    void set_cls_token(const AddedToken& cls_token) { cls_token_ = cls_token; }
};

} // namespace processors
} // namespace tokenizers
