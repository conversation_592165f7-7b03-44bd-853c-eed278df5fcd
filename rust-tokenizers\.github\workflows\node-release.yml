name: Node Release

env:
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: us-east-1

on:
  push:
    tags:
      - node-v*

jobs:
  build:
    env:
      MACOSX_DEPLOYMENT_TARGET: 10.11
    strategy:
      matrix:
        settings:
          - host: macos-latest
            target: x86_64-apple-darwin
          - host: windows-latest
            target: x86_64-pc-windows-msvc
          - host: ubuntu-latest
            target: x86_64-unknown-linux-gnu
    runs-on: ${{ matrix.settings.host }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      # Necessary for now for the cargo cache: https://github.com/actions/cache/issues/133#issuecomment-599102035
      - if: matrix.os == 'ubuntu-latest'
        run: sudo chown -R $(whoami):$(id -ng) ~/.cargo/

      - name: Cache Cargo Registry
        uses: actions/cache@v4
        with:
          path: ~/.cargo/registry
          key: ${{ runner.os }}-cargo-registry-${{ hashFiles('**/Cargo.toml') }}

      - name: Install Node ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: latest
          cache: yarn
          cache-dependency-path: ./bindings/node/

      - name: Install npm dependencies
        working-directory: ./bindings/node
        run: yarn install

      - name: Build and package rust
        working-directory: ./bindings/node
        run: |
          yarn build &&
          strip -x *.node

      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: bindings-${{ matrix.settings.target }}
          path: ${{ env.APP_NAME }}bindings/node/*.node
          if-no-files-found: error
  publish:
    name: Publish
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - uses: actions/checkout@v4
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: latest
          check-latest: true
          cache: yarn
          cache-dependency-path: ./bindings/node/
      - name: Install dependencies
        working-directory: ./bindings/node
        run: yarn install
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./bindings/node/artifacts
      - name: Move artifacts
        working-directory: ./bindings/node
        run: yarn artifacts
      - name: List packages
        working-directory: ./bindings/node
        run: ls -R ./npm
        shell: bash
      - name: Publish
        working-directory: ./bindings/node
        run: |
          echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" >> ~/.npmrc
          npm publish --access public --tag next
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
