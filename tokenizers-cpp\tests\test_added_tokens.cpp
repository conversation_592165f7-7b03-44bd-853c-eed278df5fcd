#include <gtest/gtest.h>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/added_token.hpp"

using namespace tokenizers;

// Helper function to create an empty tokenizer (similar to get_empty() in Rust)
Tokenizer create_empty_tokenizer() {
    auto model = std::make_unique<models::BPE>();
    return Tokenizer(std::move(model));
}

class AddedTokensTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup code if needed
    }
    
    void TearDown() override {
        // Cleanup code if needed
    }
};

TEST_F(AddedTokensTest, AddTokens) {
    auto tokenizer = create_empty_tokenizer();
    
    // Test adding special tokens
    std::vector<AddedToken> special_tokens = {
        AddedToken::from("<cls>", true),
        AddedToken::from("<sep>", true)
    };
    
    auto added_count = tokenizer.add_special_tokens(special_tokens);
    EXPECT_EQ(added_count, 2);
    
    // Test token to ID mapping
    auto cls_id = tokenizer.token_to_id("<cls>");
    auto sep_id = tokenizer.token_to_id("<sep>");
    
    ASSERT_TRUE(cls_id.has_value());
    ASSERT_TRUE(sep_id.has_value());
    
    // Note: The exact IDs might differ from Rust implementation
    // but they should be valid and different
    EXPECT_NE(*cls_id, *sep_id);
    
    // Test adding regular tokens
    std::vector<AddedToken> regular_tokens = {
        AddedToken::from("hello", false),
        AddedToken::from("world", false)
    };
    
    auto added_regular_count = tokenizer.add_tokens(regular_tokens);
    EXPECT_EQ(added_regular_count, 2);
    
    auto hello_id = tokenizer.token_to_id("hello");
    auto world_id = tokenizer.token_to_id("world");
    
    ASSERT_TRUE(hello_id.has_value());
    ASSERT_TRUE(world_id.has_value());
    EXPECT_NE(*hello_id, *world_id);
}

TEST_F(AddedTokensTest, TokenProperties) {
    auto tokenizer = create_empty_tokenizer();
    
    // Test AddedToken properties
    auto token_with_lstrip = AddedToken::from("<mask>", true);
    token_with_lstrip.set_lstrip(true);
    
    EXPECT_TRUE(token_with_lstrip.get_lstrip());
    EXPECT_FALSE(token_with_lstrip.get_rstrip());
    EXPECT_TRUE(token_with_lstrip.get_special());
    
    auto token_with_rstrip = AddedToken::from("<pad>", true);
    token_with_rstrip.set_rstrip(true);
    
    EXPECT_FALSE(token_with_rstrip.get_lstrip());
    EXPECT_TRUE(token_with_rstrip.get_rstrip());
    
    auto token_single_word = AddedToken::from("ing", true);
    token_single_word.set_single_word(true);
    
    EXPECT_TRUE(token_single_word.get_single_word());
}

TEST_F(AddedTokensTest, TokenNormalization) {
    auto tokenizer = create_empty_tokenizer();
    
    // Test normalized vs non-normalized tokens
    auto normalized_token = AddedToken::from("Hello", false);
    normalized_token.set_normalized(true);
    
    auto non_normalized_token = AddedToken::from("WORLD", false);
    non_normalized_token.set_normalized(false);
    
    std::vector<AddedToken> tokens = {normalized_token, non_normalized_token};
    tokenizer.add_tokens(tokens);
    
    // Both tokens should be findable
    EXPECT_TRUE(tokenizer.token_to_id("Hello").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("WORLD").has_value());
}

TEST_F(AddedTokensTest, OverlappingTokens) {
    auto tokenizer = create_empty_tokenizer();
    
    // Add overlapping special tokens
    std::vector<AddedToken> overlapping_tokens = {
        AddedToken::from("danc", true),
        AddedToken::from("nci", true),
        AddedToken::from("ing", true)
    };
    
    for (const auto& token : overlapping_tokens) {
        std::vector<AddedToken> single_token = {token};
        tokenizer.add_special_tokens(single_token);
    }
    
    // Test that all tokens are added
    EXPECT_TRUE(tokenizer.token_to_id("danc").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("nci").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("ing").has_value());
    
    // Test encoding with overlapping tokens
    // Note: The exact behavior might differ from Rust implementation
    // but the tokens should be recognized
    auto encoding = tokenizer.encode("dancing");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    bool found_danc = false, found_ing = false;
    
    for (const auto& token : tokens) {
        if (token == "danc") found_danc = true;
        if (token == "ing") found_ing = true;
    }
    
    // At least one of the overlapping tokens should be found
    EXPECT_TRUE(found_danc || found_ing);
}

TEST_F(AddedTokensTest, TokenOrder) {
    auto tokenizer = create_empty_tokenizer();
    
    // Add tokens in different orders to test priority
    std::vector<AddedToken> first_batch = {
        AddedToken::from("nci", true),
        AddedToken::from("danc", true)
    };
    
    std::vector<AddedToken> second_batch = {
        AddedToken::from("ing", true),
        AddedToken::from("ike", true)
    };
    
    tokenizer.add_special_tokens(first_batch);
    tokenizer.add_special_tokens(second_batch);
    
    // All tokens should be added successfully
    EXPECT_TRUE(tokenizer.token_to_id("nci").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("danc").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("ing").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("ike").has_value());
}

TEST_F(AddedTokensTest, VocabularySize) {
    auto tokenizer = create_empty_tokenizer();
    
    size_t initial_size = tokenizer.get_vocab_size();
    
    // Add some tokens
    std::vector<AddedToken> tokens = {
        AddedToken::from("token1", true),
        AddedToken::from("token2", true),
        AddedToken::from("token3", false)
    };
    
    tokenizer.add_special_tokens({tokens[0], tokens[1]});
    tokenizer.add_tokens({tokens[2]});
    
    size_t final_size = tokenizer.get_vocab_size();
    
    // Vocabulary size should have increased
    EXPECT_EQ(final_size, initial_size + 3);
}

TEST_F(AddedTokensTest, DuplicateTokens) {
    auto tokenizer = create_empty_tokenizer();
    
    // Add the same token twice
    std::vector<AddedToken> first_add = {AddedToken::from("duplicate", true)};
    std::vector<AddedToken> second_add = {AddedToken::from("duplicate", true)};
    
    auto first_count = tokenizer.add_special_tokens(first_add);
    auto second_count = tokenizer.add_special_tokens(second_add);
    
    EXPECT_EQ(first_count, 1);
    EXPECT_EQ(second_count, 0);  // Should not add duplicate
    
    // Token should still be findable
    EXPECT_TRUE(tokenizer.token_to_id("duplicate").has_value());
}

TEST_F(AddedTokensTest, TokenContent) {
    auto tokenizer = create_empty_tokenizer();
    
    // Test various token contents
    std::vector<AddedToken> diverse_tokens = {
        AddedToken::from("simple", false),
        AddedToken::from("<special>", true),
        AddedToken::from("with spaces", false),
        AddedToken::from("123numbers", false),
        AddedToken::from("symbols!@#", false)
    };
    
    auto added_count = tokenizer.add_tokens(diverse_tokens);
    EXPECT_EQ(added_count, diverse_tokens.size());
    
    // All tokens should be findable
    for (const auto& token : diverse_tokens) {
        EXPECT_TRUE(tokenizer.token_to_id(token.content).has_value()) 
            << "Token not found: " << token.content;
    }
}
