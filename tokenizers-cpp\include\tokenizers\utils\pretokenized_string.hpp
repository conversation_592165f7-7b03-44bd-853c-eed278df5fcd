#pragma once

#include <string>
#include <vector>

namespace tokenizers {

/**
 * @brief A string that has been pre-tokenized into splits
 * 
 * This class represents a string that has been split into multiple parts
 * during the pre-tokenization process. It maintains the splits and allows
 * further processing.
 */
class PreTokenizedString {
private:
    /// The splits of the original string
    std::vector<std::string> splits_;

public:
    /**
     * @brief Construct from a single string
     * 
     * @param text The original text
     */
    explicit PreTokenizedString(const std::string& text);
    
    /**
     * @brief Construct from existing splits
     * 
     * @param splits The pre-existing splits
     */
    explicit PreTokenizedString(const std::vector<std::string>& splits);
    
    /**
     * @brief Get the current splits
     * 
     * @return const std::vector<std::string>& The splits
     */
    const std::vector<std::string>& get_splits() const { return splits_; }
    
    /**
     * @brief Set new splits
     * 
     * @param splits The new splits
     */
    void set_splits(const std::vector<std::string>& splits) { splits_ = splits; }
    
    /**
     * @brief Get the number of splits
     * 
     * @return size_t The number of splits
     */
    size_t size() const { return splits_.size(); }
    
    /**
     * @brief Check if there are no splits
     * 
     * @return bool True if empty
     */
    bool empty() const { return splits_.empty(); }
    
    /**
     * @brief Clear all splits
     */
    void clear() { splits_.clear(); }
};

} // namespace tokenizers
