#include "tokenizers/models/unigram.hpp"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <stdexcept>
#include <cmath>
#include <limits>

namespace tokenizers {
namespace models {

Unigram::Unigram() 
    : unk_token_("<unk>")
    , min_score_(0.0) {
}

Unigram::Unigram(Vocab vocab,
                 const std::string& unk_token,
                 double min_score)
    : vocab_(std::move(vocab))
    , unk_token_(unk_token)
    , min_score_(min_score) {
    
    // Build reverse vocabulary
    for (const auto& [token, id_score] : vocab_) {
        vocab_r_[id_score.first] = token;
    }
}

Unigram Unigram::from_file(const std::string& vocab_file,
                          const std::string& unk_token) {
    // For now, return an empty model
    // In a full implementation, this would parse a JSON file
    // containing the vocabulary with scores
    return Unigram({}, unk_token);
}

Unigram Unigram::Builder::build() {
    if (!vocab_) {
        // Create a minimal vocabulary for testing
        Vocab default_vocab;
        default_vocab["<unk>"] = {0, -10.0};
        vocab_ = default_vocab;
    }
    
    return Unigram(*vocab_, unk_token_, min_score_);
}

std::vector<Token> Unigram::tokenize(const std::string& sequence) {
    if (sequence.empty()) {
        return {};
    }
    
    // Use Viterbi algorithm to find the best segmentation
    auto segmentation = viterbi_search(sequence);
    
    // Convert segmentation to tokens
    return segmentation_to_tokens(sequence, segmentation);
}

std::optional<uint32_t> Unigram::token_to_id(const std::string& token) const {
    auto it = vocab_.find(token);
    return it != vocab_.end() ? std::make_optional(it->second.first) : std::nullopt;
}

std::optional<std::string> Unigram::id_to_token(uint32_t id) const {
    auto it = vocab_r_.find(id);
    return it != vocab_r_.end() ? std::make_optional(it->second) : std::nullopt;
}

std::unordered_map<std::string, uint32_t> Unigram::get_vocab() const {
    std::unordered_map<std::string, uint32_t> result;
    for (const auto& [token, id_score] : vocab_) {
        result[token] = id_score.first;
    }
    return result;
}

size_t Unigram::get_vocab_size() const {
    return vocab_.size();
}

std::unique_ptr<trainers::Trainer> Unigram::get_trainer() {
    // TODO: Implement Unigram trainer
    return nullptr;
}

void Unigram::save(const std::string& path, bool pretty) const {
    std::ofstream file(path);
    if (!file.is_open()) {
        throw std::runtime_error("Could not open file for writing: " + path);
    }
    
    // Simple JSON output for vocabulary with scores
    file << "{\n";
    file << "  \"type\": \"Unigram\",\n";
    file << "  \"unk_token\": \"" << unk_token_ << "\",\n";
    file << "  \"min_score\": " << min_score_ << ",\n";
    file << "  \"vocab\": {\n";
    
    bool first = true;
    for (const auto& [token, id_score] : vocab_) {
        if (!first) file << ",\n";
        file << "    \"" << token << "\": {\"id\": " << id_score.first 
             << ", \"score\": " << id_score.second << "}";
        first = false;
    }
    
    file << "\n  }\n";
    file << "}\n";
}

std::unique_ptr<Model> Unigram::clone() const {
    return std::make_unique<Unigram>(vocab_, unk_token_, min_score_);
}

std::vector<std::string> Unigram::viterbi_search(const std::string& text) const {
    if (text.empty()) {
        return {};
    }
    
    size_t n = text.length();
    
    // DP table: dp[i] = (best_score, best_previous_position)
    std::vector<std::pair<double, int>> dp(n + 1, {-std::numeric_limits<double>::infinity(), -1});
    dp[0] = {0.0, -1};
    
    // Fill DP table
    for (size_t i = 0; i < n; ++i) {
        if (dp[i].first == -std::numeric_limits<double>::infinity()) {
            continue;
        }
        
        // Try all possible substrings starting at position i
        for (size_t j = i + 1; j <= n; ++j) {
            std::string substr = text.substr(i, j - i);
            double score = get_score(substr);
            
            double new_score = dp[i].first + score;
            if (new_score > dp[j].first) {
                dp[j] = {new_score, static_cast<int>(i)};
            }
        }
    }
    
    // Backtrack to get the segmentation
    std::vector<std::string> segmentation;
    int pos = static_cast<int>(n);
    
    while (pos > 0) {
        int prev_pos = dp[pos].second;
        if (prev_pos == -1) {
            // This shouldn't happen if we have a valid path
            break;
        }
        
        std::string token = text.substr(prev_pos, pos - prev_pos);
        segmentation.push_back(token);
        pos = prev_pos;
    }
    
    // Reverse to get correct order
    std::reverse(segmentation.begin(), segmentation.end());
    
    return segmentation;
}

double Unigram::get_score(const std::string& token) const {
    auto it = vocab_.find(token);
    if (it != vocab_.end()) {
        return it->second.second;
    }
    
    // If token not found, return a very low score
    // In practice, we might want to handle unknown characters differently
    return -20.0;
}

std::vector<Token> Unigram::segmentation_to_tokens(const std::string& text,
                                                  const std::vector<std::string>& segmentation) const {
    std::vector<Token> tokens;
    size_t current_offset = 0;
    
    for (const auto& segment : segmentation) {
        auto id_opt = token_to_id(segment);
        uint32_t id;
        
        if (id_opt) {
            id = *id_opt;
        } else {
            // Use unknown token ID
            auto unk_id_opt = token_to_id(unk_token_);
            id = unk_id_opt ? *unk_id_opt : 0;
        }
        
        size_t segment_length = segment.length();
        tokens.emplace_back(id, segment, std::make_pair(current_offset, current_offset + segment_length));
        current_offset += segment_length;
    }
    
    return tokens;
}

bool Unigram::is_valid_char(char c) const {
    // Simple validation - in practice, this might be more sophisticated
    return c >= 0;  // Accept all non-negative chars
}

} // namespace models
} // namespace tokenizers
