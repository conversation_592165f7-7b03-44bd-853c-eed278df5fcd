# Decoders

<tokenizerslangcontent>
<python>
## BPEDecoder

[[autodoc]] tokenizers.decoders.BPEDecoder

## ByteLevel

[[autodoc]] tokenizers.decoders.ByteLevel

## CTC

[[autodoc]] tokenizers.decoders.CTC

## Metaspace

[[autodoc]] tokenizers.decoders.Metaspace

## WordPiece

[[autodoc]] tokenizers.decoders.WordPiece
</python>
<rust>
The Rust API Reference is available directly on the [Docs.rs](https://docs.rs/tokenizers/latest/tokenizers/) website.
</rust>
<node>
The node API has not been documented yet.
</node>
</tokenizerslangcontent>