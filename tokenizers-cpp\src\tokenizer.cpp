#include "tokenizers/tokenizer.hpp"
#include "tokenizers/utils/pretokenized_string.hpp"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <stdexcept>

namespace tokenizers {

// Forward declarations for helper classes we'll implement later
class NormalizedString {
public:
    explicit NormalizedString(const std::string& text) : original_(text), normalized_(text) {}
    
    const std::string& get() const { return normalized_; }
    void set(const std::string& text) { normalized_ = text; }
    const std::string& original() const { return original_; }
    size_t len() const { return normalized_.length(); }
    
private:
    std::string original_;
    std::string normalized_;
};



Tokenizer::Tokenizer(std::unique_ptr<models::Model> model)
    : model_(std::move(model)) {
    if (!model_) {
        throw std::invalid_argument("Model cannot be null");
    }
}

void Tokenizer::set_normalizer(std::unique_ptr<normalizers::Normalizer> normalizer) {
    normalizer_ = std::move(normalizer);
}

void Tokenizer::set_pre_tokenizer(std::unique_ptr<pre_tokenizers::PreTokenizer> pre_tokenizer) {
    pre_tokenizer_ = std::move(pre_tokenizer);
}

void Tokenizer::set_post_processor(std::unique_ptr<processors::PostProcessor> post_processor) {
    post_processor_ = std::move(post_processor);
}

void Tokenizer::set_decoder(std::unique_ptr<decoders::Decoder> decoder) {
    decoder_ = std::move(decoder);
}

void Tokenizer::set_truncation(const std::optional<TruncationParams>& truncation) {
    truncation_ = truncation;
}

void Tokenizer::set_padding(const std::optional<PaddingParams>& padding) {
    padding_ = padding;
}

Encoding Tokenizer::encode(const std::string& sequence,
                          const std::optional<std::string>& pair,
                          bool add_special_tokens) {
    // Step 1: Normalize the input
    NormalizedString normalized(sequence);
    if (normalizer_) {
        normalizer_->normalize(normalized);
    }
    
    // Handle added tokens
    auto added_tokens = added_vocabulary_.extract_and_normalize(normalized);
    
    // Step 2: Pre-tokenize
    PreTokenizedString pretokenized(normalized.get());
    if (pre_tokenizer_) {
        pre_tokenizer_->pre_tokenize(pretokenized);
    }
    
    // Split on added tokens
    added_vocabulary_.split_pretokenized(pretokenized);
    
    // Step 3: Apply the model to each split
    std::vector<Token> all_tokens;
    for (const auto& split : pretokenized.get_splits()) {
        auto tokens = model_->tokenize(split);
        all_tokens.insert(all_tokens.end(), tokens.begin(), tokens.end());
    }
    
    // Add the extracted added tokens
    all_tokens.insert(all_tokens.end(), added_tokens.begin(), added_tokens.end());
    
    // Step 4: Create encoding
    Encoding encoding = Encoding::from_tokens(all_tokens);
    
    // Step 5: Handle pair if provided
    std::optional<Encoding> pair_encoding;
    if (pair) {
        pair_encoding = encode(*pair, std::nullopt, false);  // Don't add special tokens to pair separately
    }
    
    // Step 6: Post-process
    if (post_processor_) {
        encoding = post_processor_->process(encoding, pair_encoding, add_special_tokens);
    }
    
    // Step 7: Apply truncation and padding
    apply_truncation(encoding);
    apply_padding(encoding);
    
    return encoding;
}

std::vector<Encoding> Tokenizer::encode_batch(const std::vector<std::string>& sequences,
                                            bool add_special_tokens) {
    std::vector<Encoding> encodings;
    encodings.reserve(sequences.size());
    
    for (const auto& sequence : sequences) {
        encodings.push_back(encode(sequence, std::nullopt, add_special_tokens));
    }
    
    // Apply batch padding if needed
    apply_padding_batch(encodings);
    
    return encodings;
}

std::string Tokenizer::decode(const std::vector<uint32_t>& ids, bool skip_special_tokens) {
    std::vector<std::string> tokens;
    tokens.reserve(ids.size());
    
    for (uint32_t id : ids) {
        // Check if it's a special token and should be skipped
        if (skip_special_tokens && added_vocabulary_.is_special_token(id)) {
            continue;
        }
        
        // Try to get token from added vocabulary first
        auto added_token = added_vocabulary_.id_to_token(id);
        if (added_token) {
            tokens.push_back(*added_token);
            continue;
        }
        
        // Get token from model
        auto model_token = model_->id_to_token(id);
        if (model_token) {
            tokens.push_back(*model_token);
        }
    }
    
    // Apply decoder if available
    if (decoder_) {
        return decoder_->decode(tokens);
    }
    
    // Simple concatenation fallback
    std::string result;
    for (const auto& token : tokens) {
        result += token;
    }
    return result;
}

std::vector<std::string> Tokenizer::decode_batch(const std::vector<std::vector<uint32_t>>& sequences,
                                               bool skip_special_tokens) {
    std::vector<std::string> results;
    results.reserve(sequences.size());
    
    for (const auto& sequence : sequences) {
        results.push_back(decode(sequence, skip_special_tokens));
    }
    
    return results;
}

void Tokenizer::train_from_files(trainers::Trainer& trainer, const std::vector<std::string>& files) {
    std::vector<std::string> sequences;
    
    for (const auto& file : files) {
        std::ifstream infile(file);
        if (!infile.is_open()) {
            throw std::runtime_error("Could not open file: " + file);
        }
        
        std::string line;
        while (std::getline(infile, line)) {
            if (!line.empty()) {
                sequences.push_back(line);
            }
        }
    }
    
    train(trainer, sequences);
}

void Tokenizer::train(trainers::Trainer& trainer, const std::vector<std::string>& sequences) {
    // Feed the sequences to the trainer
    trainer.feed(sequences);
    
    // Train the model
    auto special_tokens = trainer.train(*model_);
    
    // Add special tokens to the added vocabulary
    for (const auto& token : special_tokens) {
        added_vocabulary_.add_token(token);
    }
}

void Tokenizer::save(const std::string& path, bool pretty) const {
    // TODO: Implement JSON serialization
    // This is a placeholder implementation
    std::ofstream file(path);
    if (!file.is_open()) {
        throw std::runtime_error("Could not open file for writing: " + path);
    }
    
    file << "{\n";
    file << "  \"version\": \"1.0\",\n";
    file << "  \"model\": {\n";
    file << "    \"type\": \"" << model_->get_type() << "\"\n";
    file << "  }\n";
    file << "}\n";
}

std::unique_ptr<Tokenizer> Tokenizer::from_file(const std::string& path) {
    // TODO: Implement JSON deserialization
    // This is a placeholder implementation
    throw std::runtime_error("from_file not yet implemented");
}

size_t Tokenizer::add_tokens(const std::vector<std::string>& tokens) {
    return added_vocabulary_.add_tokens(tokens).size();
}

size_t Tokenizer::add_special_tokens(const std::vector<AddedToken>& tokens) {
    return added_vocabulary_.add_tokens(tokens).size();
}

std::optional<uint32_t> Tokenizer::token_to_id(const std::string& token) const {
    // Check added vocabulary first
    auto added_id = added_vocabulary_.token_to_id(token);
    if (added_id) {
        return added_id;
    }
    
    // Check model vocabulary
    return model_->token_to_id(token);
}

std::optional<std::string> Tokenizer::id_to_token(uint32_t id) const {
    // Check added vocabulary first
    auto added_token = added_vocabulary_.id_to_token(id);
    if (added_token) {
        return added_token;
    }
    
    // Check model vocabulary
    return model_->id_to_token(id);
}

size_t Tokenizer::get_vocab_size(bool with_added_tokens) const {
    size_t size = model_->get_vocab_size();
    if (with_added_tokens) {
        size += added_vocabulary_.size();
    }
    return size;
}

std::unordered_map<std::string, uint32_t> Tokenizer::get_vocab(bool with_added_tokens) const {
    auto vocab = model_->get_vocab();
    
    if (with_added_tokens) {
        const auto& added_vocab = added_vocabulary_.get_vocab();
        vocab.insert(added_vocab.begin(), added_vocab.end());
    }
    
    return vocab;
}

std::unique_ptr<Tokenizer> Tokenizer::clone() const {
    auto cloned = std::make_unique<Tokenizer>(model_->clone());
    
    if (normalizer_) {
        cloned->set_normalizer(normalizer_->clone());
    }
    if (pre_tokenizer_) {
        cloned->set_pre_tokenizer(pre_tokenizer_->clone());
    }
    if (post_processor_) {
        cloned->set_post_processor(post_processor_->clone());
    }
    if (decoder_) {
        cloned->set_decoder(decoder_->clone());
    }
    
    cloned->added_vocabulary_ = added_vocabulary_;
    cloned->truncation_ = truncation_;
    cloned->padding_ = padding_;
    
    return cloned;
}

void Tokenizer::apply_truncation(Encoding& encoding) const {
    if (!truncation_) {
        return;
    }
    
    encoding.truncate(truncation_->max_length, truncation_->stride, truncation_->direction);
}

void Tokenizer::apply_padding(Encoding& encoding) const {
    if (!padding_ || padding_->strategy == "do_not_pad") {
        return;
    }
    
    size_t target_length = 0;
    if (padding_->strategy == "max_length") {
        // Use a default max length if not specified
        target_length = truncation_ ? truncation_->max_length : 512;
    } else {
        // For single encoding, no padding needed unless max_length is specified
        return;
    }
    
    encoding.pad(target_length, padding_->pad_id, padding_->pad_type_id, 
                padding_->pad_token, padding_->direction);
}

void Tokenizer::apply_padding_batch(std::vector<Encoding>& encodings) const {
    if (!padding_ || padding_->strategy == "do_not_pad" || encodings.empty()) {
        return;
    }
    
    size_t target_length = 0;
    if (padding_->strategy == "longest") {
        // Find the longest sequence
        for (const auto& encoding : encodings) {
            target_length = std::max(target_length, encoding.size());
        }
    } else if (padding_->strategy == "max_length") {
        target_length = truncation_ ? truncation_->max_length : 512;
    }
    
    // Apply padding to all encodings
    for (auto& encoding : encodings) {
        encoding.pad(target_length, padding_->pad_id, padding_->pad_type_id,
                    padding_->pad_token, padding_->direction);
    }
}

} // namespace tokenizers
