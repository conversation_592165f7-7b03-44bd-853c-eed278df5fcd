#include "tokenizers/decoders/bpe_decoder.hpp"
#include <sstream>

namespace tokenizers {
namespace decoders {

BPEDecoder::BPEDecoder(const std::string& suffix)
    : suffix_(suffix) {
}

std::string BPEDecoder::decode(const std::vector<std::string>& tokens) {
    if (tokens.empty()) {
        return "";
    }
    
    std::ostringstream result;
    
    for (size_t i = 0; i < tokens.size(); ++i) {
        std::string token = tokens[i];
        
        // Add suffix if specified
        if (!suffix_.empty()) {
            token += suffix_;
        }
        
        // For BPE, we typically just concatenate tokens
        // In a more sophisticated implementation, we might handle
        // special merge markers or spacing rules
        result << token;
        
        // Add space between tokens (except for the last one)
        // This is a simple heuristic - in practice, BPE decoders
        // might have more sophisticated spacing rules
        if (i < tokens.size() - 1) {
            // Don't add space if the next token starts with a special marker
            // or if the current token ends with a special marker
            bool should_add_space = true;
            
            // Simple heuristic: don't add space if tokens look like they should be merged
            if (i + 1 < tokens.size()) {
                const std::string& next_token = tokens[i + 1];
                // If next token starts with ## (WordPiece style) or similar markers
                if (next_token.length() >= 2 && next_token.substr(0, 2) == "##") {
                    should_add_space = false;
                }
                // If current token is very short (likely a character)
                if (token.length() == 1) {
                    should_add_space = false;
                }
            }
            
            if (should_add_space) {
                result << " ";
            }
        }
    }
    
    return result.str();
}

std::unique_ptr<Decoder> BPEDecoder::clone() const {
    return std::make_unique<BPEDecoder>(suffix_);
}

} // namespace decoders
} // namespace tokenizers
