# Visualizer

<tokenizerslangcontent>
<python>
## Annotation

[[autodoc]] tokenizers.tools.Annotation

## EncodingVisualizer

[[autodoc]] tokenizers.tools.EncodingVisualizer
    -  __call__
</python>
<rust>
The Rust API Reference is available directly on the [Docs.rs](https://docs.rs/tokenizers/latest/tokenizers/) website.
</rust>
<node>
The node API has not been documented yet.
</node>
</tokenizerslangcontent>