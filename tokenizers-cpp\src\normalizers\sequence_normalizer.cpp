#include "tokenizers/normalizers/sequence_normalizer.hpp"

namespace tokenizers {

// Forward declaration from bert_normalizer.cpp
class NormalizedString {
public:
    explicit NormalizedString(const std::string& text) : text_(text) {}
    
    const std::string& get() const { return text_; }
    void set(const std::string& text) { text_ = text; }
    
private:
    std::string text_;
};

namespace normalizers {

SequenceNormalizer::SequenceNormalizer(std::vector<std::unique_ptr<Normalizer>> normalizers)
    : normalizers_(std::move(normalizers)) {
}

SequenceNormalizer::SequenceNormalizer(const SequenceNormalizer& other) {
    normalizers_.reserve(other.normalizers_.size());
    for (const auto& normalizer : other.normalizers_) {
        normalizers_.push_back(normalizer->clone());
    }
}

SequenceNormalizer& SequenceNormalizer::operator=(const SequenceNormalizer& other) {
    if (this != &other) {
        normalizers_.clear();
        normalizers_.reserve(other.normalizers_.size());
        for (const auto& normalizer : other.normalizers_) {
            normalizers_.push_back(normalizer->clone());
        }
    }
    return *this;
}

void SequenceNormalizer::normalize(NormalizedString& normalized) {
    // Apply each normalizer in sequence
    for (const auto& normalizer : normalizers_) {
        normalizer->normalize(normalized);
    }
}

std::unique_ptr<Normalizer> SequenceNormalizer::clone() const {
    std::vector<std::unique_ptr<Normalizer>> cloned_normalizers;
    cloned_normalizers.reserve(normalizers_.size());
    
    for (const auto& normalizer : normalizers_) {
        cloned_normalizers.push_back(normalizer->clone());
    }
    
    return std::make_unique<SequenceNormalizer>(std::move(cloned_normalizers));
}

void SequenceNormalizer::add_normalizer(std::unique_ptr<Normalizer> normalizer) {
    normalizers_.push_back(std::move(normalizer));
}

} // namespace normalizers
} // namespace tokenizers
