#include <iostream>
#include <vector>
#include <string>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/trainers/bpe_trainer.hpp"

using namespace tokenizers;

int main() {
    std::cout << "Tokenizers C++ Basic Usage Example\n";
    std::cout << "==================================\n\n";
    
    try {
        // Create a simple BPE model with a basic vocabulary
        models::BPE::Vocab vocab = {
            // Single characters
            {"h", 0}, {"e", 1}, {"l", 2}, {"o", 3}, {" ", 4}, {"w", 5}, {"r", 6}, {"d", 7},
            {"!", 8}, {"t", 9}, {"s", 10}, {"i", 11}, {"n", 12}, {"g", 13}, {"a", 14}, {"m", 15},
            {"p", 16}, {"x", 17}, {"c", 18}, {"u", 19}, {"f", 20}, {"y", 21}, {"b", 22}, {"k", 23},
            
            // Common merges
            {"he", 24}, {"ll", 25}, {"lo", 26}, {"wo", 27}, {"or", 28}, {"rl", 29}, {"ld", 30},
            {"th", 31}, {"in", 32}, {"ng", 33}, {"an", 34}, {"am", 35}, {"pl", 36}, {"le", 37},
            {"ex", 38}, {"xa", 39}, {"mp", 40}, {"is", 41}, {"st", 42}, {"te", 43}, {"es", 44},
            
            // Word-level tokens
            {"hello", 45}, {"world", 46}, {"this", 47}, {"test", 48}, {"example", 49}
        };
        
        models::BPE::Merges merges = {
            {"h", "e"},      // he
            {"l", "l"},      // ll
            {"l", "o"},      // lo
            {"w", "o"},      // wo
            {"o", "r"},      // or
            {"r", "l"},      // rl
            {"l", "d"},      // ld
            {"t", "h"},      // th
            {"i", "n"},      // in
            {"n", "g"},      // ng
            {"a", "n"},      // an
            {"a", "m"},      // am
            {"p", "l"},      // pl
            {"l", "e"},      // le
            {"e", "x"},      // ex
            {"x", "a"},      // xa
            {"m", "p"},      // mp
            {"i", "s"},      // is
            {"s", "t"},      // st
            {"t", "e"},      // te
            {"e", "s"},      // es
        };
        
        // Create BPE model
        auto bpe_model = std::make_unique<models::BPE>(vocab, merges);
        std::cout << "Created BPE model with vocabulary size: " << bpe_model->get_vocab_size() << "\n\n";
        
        // Create tokenizer
        Tokenizer tokenizer(std::move(bpe_model));
        
        // Add some special tokens
        std::vector<AddedToken> special_tokens = {
            AddedToken::from("<unk>", true),
            AddedToken::from("<pad>", true),
            AddedToken::from("<s>", true),
            AddedToken::from("</s>", true)
        };
        
        auto added_count = tokenizer.add_special_tokens(special_tokens);
        std::cout << "Added " << added_count << " special tokens\n\n";
        
        // Test encoding
        std::vector<std::string> test_sentences = {
            "hello world",
            "this is a test",
            "example text",
            "hello there world!"
        };
        
        std::cout << "Encoding Examples:\n";
        std::cout << "------------------\n";
        
        for (const auto& sentence : test_sentences) {
            std::cout << "Input: \"" << sentence << "\"\n";
            
            auto encoding = tokenizer.encode(sentence);
            
            const auto& tokens = encoding.get_tokens();
            const auto& ids = encoding.get_ids();
            const auto& offsets = encoding.get_offsets();
            
            std::cout << "Tokens: [";
            for (size_t i = 0; i < tokens.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << tokens[i] << "\"";
            }
            std::cout << "]\n";
            
            std::cout << "IDs: [";
            for (size_t i = 0; i < ids.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << ids[i];
            }
            std::cout << "]\n";
            
            std::cout << "Offsets: [";
            for (size_t i = 0; i < offsets.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "(" << offsets[i].first << ", " << offsets[i].second << ")";
            }
            std::cout << "]\n";
            
            // Test decoding
            auto decoded = tokenizer.decode(ids);
            std::cout << "Decoded: \"" << decoded << "\"\n";
            
            std::cout << "\n";
        }
        
        // Test batch encoding
        std::cout << "Batch Encoding Example:\n";
        std::cout << "-----------------------\n";
        
        auto batch_encodings = tokenizer.encode_batch(test_sentences);
        
        std::cout << "Encoded " << batch_encodings.size() << " sentences:\n";
        for (size_t i = 0; i < batch_encodings.size(); ++i) {
            const auto& encoding = batch_encodings[i];
            std::cout << "Sentence " << (i + 1) << ": " << encoding.size() << " tokens\n";
        }
        std::cout << "\n";
        
        // Test batch decoding
        std::vector<std::vector<uint32_t>> id_sequences;
        for (const auto& encoding : batch_encodings) {
            id_sequences.push_back(encoding.get_ids());
        }
        
        auto batch_decoded = tokenizer.decode_batch(id_sequences);
        
        std::cout << "Batch Decoding Results:\n";
        std::cout << "-----------------------\n";
        for (size_t i = 0; i < batch_decoded.size(); ++i) {
            std::cout << "Original: \"" << test_sentences[i] << "\"\n";
            std::cout << "Decoded:  \"" << batch_decoded[i] << "\"\n\n";
        }
        
        // Test vocabulary access
        std::cout << "Vocabulary Information:\n";
        std::cout << "----------------------\n";
        std::cout << "Total vocabulary size: " << tokenizer.get_vocab_size() << "\n";
        
        // Test some token-to-ID mappings
        std::vector<std::string> test_tokens = {"hello", "world", "<unk>", "<pad>"};
        for (const auto& token : test_tokens) {
            auto id = tokenizer.token_to_id(token);
            if (id) {
                std::cout << "\"" << token << "\" -> ID " << *id << "\n";
                
                auto back_token = tokenizer.id_to_token(*id);
                if (back_token) {
                    std::cout << "ID " << *id << " -> \"" << *back_token << "\"\n";
                }
            } else {
                std::cout << "\"" << token << "\" -> Not found\n";
            }
        }
        
        std::cout << "\nExample completed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
