name: Node
on:
  push:
    branches:
      - main
    paths-ignore:
      - bindings/python/**
  pull_request:
    paths-ignore:
      - bindings/python/**

jobs:
  build_and_test:
    name: Check everything builds
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      # Necessary for now for the cargo cache: https://github.com/actions/cache/issues/133#issuecomment-599102035
      - run: sudo chown -R $(whoami):$(id -ng) ~/.cargo/

      - name: Cache Cargo Registry
        uses: actions/cache@v4
        with:
          path: ~/.cargo/registry
          key: ${{ runner.os }}-cargo-registry-${{ hashFiles('**/Cargo.lock') }}

      - name: Install Node
        uses: actions/setup-node@v4
        with:
          node-version: latest
      - name: Install dependencies
        working-directory: ./bindings/node
        run: yarn install

      - name: Build all
        working-directory: ./bindings/node
        run: yarn build

      - name: <PERSON><PERSON> Rust formatting
        uses: actions-rs/cargo@v1
        with:
          command: fmt
          args: --manifest-path ./bindings/node/Cargo.toml -- --check

      - name: <PERSON><PERSON> Rust with <PERSON>lip<PERSON>
        uses: actions-rs/cargo@v1
        with:
          command: clippy
          args: --manifest-path ./bindings/node/Cargo.toml --all-targets --all-features -- -D warnings

      - name: Lint TS
        working-directory: ./bindings/node
        run: yarn lint

      - name: Run JS tests
        working-directory: ./bindings/node
        run: make test
