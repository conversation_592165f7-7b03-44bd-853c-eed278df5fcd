#pragma once

#include "decoder.hpp"
#include <string>
#include <vector>
#include <memory>

namespace tokenizers {
namespace decoders {

/**
 * @brief WordPiece Decoder
 * 
 * This decoder is responsible for decoding WordPiece tokens back to text.
 * It handles the merging of subword tokens with the "##" prefix.
 */
class WordPieceDecoder : public Decoder {
private:
    /// Prefix used for continuing subwords
    std::string prefix_;
    
    /// Whether to cleanup extra spaces
    bool cleanup_;

public:
    /**
     * @brief Construct a new WordPieceDecoder
     * 
     * @param prefix The prefix used for continuing subwords (default: "##")
     * @param cleanup Whether to cleanup extra spaces (default: true)
     */
    explicit WordPieceDecoder(const std::string& prefix = "##", bool cleanup = true);
    
    /**
     * @brief Builder class for WordPieceDecoder
     */
    class Builder {
    private:
        std::string prefix_ = "##";
        bool cleanup_ = true;
        
    public:
        Builder() = default;
        
        Builder& prefix(const std::string& prefix) { prefix_ = prefix; return *this; }
        Builder& cleanup(bool cleanup) { cleanup_ = cleanup; return *this; }
        
        WordPieceDecoder build() {
            return WordPieceDecoder(prefix_, cleanup_);
        }
    };
    
    /**
     * @brief Create a builder for WordPieceDecoder
     * 
     * @return Builder A WordPieceDecoder builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Decoder interface implementation
    std::string decode(const std::vector<std::string>& tokens) override;
    std::unique_ptr<Decoder> clone() const override;
    std::string get_type() const override { return "WordPieceDecoder"; }
    
    // WordPieceDecoder-specific methods
    
    /**
     * @brief Get the prefix
     * 
     * @return const std::string& The prefix
     */
    const std::string& get_prefix() const { return prefix_; }
    
    /**
     * @brief Set the prefix
     * 
     * @param prefix The prefix to set
     */
    void set_prefix(const std::string& prefix) { prefix_ = prefix; }
    
    /**
     * @brief Get the cleanup flag
     * 
     * @return bool Whether cleanup is enabled
     */
    bool get_cleanup() const { return cleanup_; }
    
    /**
     * @brief Set the cleanup flag
     * 
     * @param cleanup Whether to enable cleanup
     */
    void set_cleanup(bool cleanup) { cleanup_ = cleanup; }

private:
    /**
     * @brief Clean up extra spaces in the decoded text
     * 
     * @param text The text to clean up
     * @return std::string The cleaned text
     */
    std::string cleanup_text(const std::string& text) const;
};

} // namespace decoders
} // namespace tokenizers
