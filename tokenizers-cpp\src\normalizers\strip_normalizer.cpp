#include "tokenizers/normalizers/strip_normalizer.hpp"
#include <algorithm>
#include <cctype>

namespace tokenizers {

// Forward declaration from bert_normalizer.cpp
class NormalizedString {
public:
    explicit NormalizedString(const std::string& text) : text_(text) {}
    
    const std::string& get() const { return text_; }
    void set(const std::string& text) { text_ = text; }
    
private:
    std::string text_;
};

namespace normalizers {

StripNormalizer::StripNormalizer(bool left, bool right)
    : left_(left), right_(right) {
}

void StripNormalizer::normalize(NormalizedString& normalized) {
    std::string text = normalized.get();
    
    if (text.empty()) {
        return;
    }
    
    size_t start = 0;
    size_t end = text.length();
    
    // Strip from the left
    if (left_) {
        while (start < end && is_whitespace(text[start])) {
            start++;
        }
    }
    
    // Strip from the right
    if (right_) {
        while (end > start && is_whitespace(text[end - 1])) {
            end--;
        }
    }
    
    // Extract the stripped substring
    if (start > 0 || end < text.length()) {
        text = text.substr(start, end - start);
        normalized.set(text);
    }
}

std::unique_ptr<Normalizer> StripNormalizer::clone() const {
    return std::make_unique<StripNormalizer>(left_, right_);
}

bool StripNormalizer::is_whitespace(char c) const {
    return std::isspace(static_cast<unsigned char>(c));
}

} // namespace normalizers
} // namespace tokenizers
