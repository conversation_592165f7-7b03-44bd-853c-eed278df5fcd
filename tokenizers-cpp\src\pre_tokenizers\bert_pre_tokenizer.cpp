#include "tokenizers/pre_tokenizers/bert_pre_tokenizer.hpp"
#include <cctype>
#include <algorithm>

namespace tokenizers {
namespace pre_tokenizers {

void BertPreTokenizer::pre_tokenize(PreTokenizedString& pretokenized) {
    const auto& current_splits = pretokenized.get_splits();
    std::vector<std::string> new_splits;
    
    for (const auto& split : current_splits) {
        auto bert_splits = bert_split(split);
        new_splits.insert(new_splits.end(), bert_splits.begin(), bert_splits.end());
    }
    
    pretokenized.set_splits(new_splits);
}

std::unique_ptr<PreTokenizer> BertPreTokenizer::clone() const {
    return std::make_unique<BertPreTokenizer>();
}

bool BertPreTokenizer::is_whitespace(char c) const {
    return std::isspace(static_cast<unsigned char>(c));
}

bool BertPreTokenizer::is_punctuation(char c) const {
    // Check for ASCII punctuation
    if (std::ispunct(static_cast<unsigned char>(c))) {
        return true;
    }
    
    // Check for extended punctuation (simplified)
    unsigned char uc = static_cast<unsigned char>(c);
    return (uc >= 0x21 && uc <= 0x2F) ||  // !"#$%&'()*+,-./
           (uc >= 0x3A && uc <= 0x40) ||  // :;<=>?@
           (uc >= 0x5B && uc <= 0x60) ||  // [\]^_`
           (uc >= 0x7B && uc <= 0x7E);    // {|}~
}

bool BertPreTokenizer::is_chinese_char(unsigned char c) const {
    // Simplified check for Chinese characters
    // In a full implementation, this would check Unicode ranges for CJK characters
    return c >= 0x80;  // Very basic check - anything in extended ASCII range
}

std::vector<std::string> BertPreTokenizer::bert_split(const std::string& text) const {
    if (text.empty()) {
        return {};
    }
    
    // First split on whitespace
    auto whitespace_tokens = whitespace_split(text);
    
    // Then split each token on punctuation
    std::vector<std::string> final_tokens;
    for (const auto& token : whitespace_tokens) {
        auto punct_tokens = punctuation_split(token);
        final_tokens.insert(final_tokens.end(), punct_tokens.begin(), punct_tokens.end());
    }
    
    return final_tokens;
}

std::vector<std::string> BertPreTokenizer::whitespace_split(const std::string& text) const {
    std::vector<std::string> tokens;

    if (text.empty()) {
        return tokens;
    }

    size_t start = 0;

    while (start < text.length()) {
        // Skip leading whitespace
        while (start < text.length() && is_whitespace(text[start])) {
            start++;
        }

        // If we've reached the end, break
        if (start >= text.length()) {
            break;
        }

        // Find the end of the current token
        size_t end = start;
        while (end < text.length() && !is_whitespace(text[end])) {
            end++;
        }

        // Add the token
        tokens.push_back(text.substr(start, end - start));

        // Move to the next position
        start = end;
    }

    return tokens;
}

std::vector<std::string> BertPreTokenizer::punctuation_split(const std::string& text) const {
    std::vector<std::string> tokens;
    
    if (text.empty()) {
        return tokens;
    }
    
    std::string current_token;
    
    for (size_t i = 0; i < text.length(); ++i) {
        char c = text[i];
        
        if (is_punctuation(c)) {
            // Add current token if not empty
            if (!current_token.empty()) {
                tokens.push_back(current_token);
                current_token.clear();
            }
            
            // Add punctuation as separate token
            tokens.push_back(std::string(1, c));
        } else if (is_chinese_char(static_cast<unsigned char>(c))) {
            // Add current token if not empty
            if (!current_token.empty()) {
                tokens.push_back(current_token);
                current_token.clear();
            }
            
            // Add Chinese character as separate token
            tokens.push_back(std::string(1, c));
        } else {
            // Add to current token
            current_token += c;
        }
    }
    
    // Add final token if not empty
    if (!current_token.empty()) {
        tokens.push_back(current_token);
    }
    
    return tokens;
}

} // namespace pre_tokenizers
} // namespace tokenizers
