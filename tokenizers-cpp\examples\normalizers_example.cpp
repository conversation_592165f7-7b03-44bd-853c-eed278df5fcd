#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/normalizers/bert_normalizer.hpp"
#include "tokenizers/normalizers/strip_normalizer.hpp"
#include "tokenizers/normalizers/sequence_normalizer.hpp"

using namespace tokenizers;

int main() {
    std::cout << "Normalizers Example\n";
    std::cout << "==================\n\n";
    
    try {
        // Create a simple BPE model for testing
        models::BPE::Vocab vocab = {
            {"h", 0}, {"e", 1}, {"l", 2}, {"o", 3}, {" ", 4}, {"w", 5}, {"r", 6}, {"d", 7},
            {"t", 8}, {"s", 9}, {"i", 10}, {"n", 11}, {"g", 12}, {"a", 13}, {"m", 14}, {"p", 15},
            {"x", 16}, {"c", 17}, {"u", 18}, {"f", 19}, {"y", 20}, {"b", 21}, {"k", 22}, {"j", 23},
            {"he", 24}, {"ll", 25}, {"lo", 26}, {"wo", 27}, {"or", 28}, {"rl", 29}, {"ld", 30},
            {"th", 31}, {"in", 32}, {"ng", 33}, {"an", 34}, {"am", 35}, {"te", 36}, {"st", 37}
        };
        
        models::BPE::Merges merges = {
            {"h", "e"}, {"l", "l"}, {"l", "o"}, {"w", "o"}, {"o", "r"}, {"r", "l"}, {"l", "d"},
            {"t", "h"}, {"i", "n"}, {"n", "g"}, {"a", "n"}, {"a", "m"}, {"t", "e"}, {"s", "t"}
        };
        
        // Test strings with various normalization needs
        std::vector<std::string> test_strings = {
            "  Hello World!  ",           // Leading/trailing whitespace
            "HELLO WORLD",                // Uppercase
            "Héllo Wörld",               // Accented characters
            "Hello\tWorld\n",            // Control characters
            "  MIXED   Case  TEXT  ",    // Mixed case with extra spaces
            "Café naïve résumé"          // Multiple accents
        };
        
        std::cout << "Example 1: Individual Normalizers\n";
        std::cout << "---------------------------------\n\n";
        
        // Test 1: Strip Normalizer
        std::cout << "Strip Normalizer (both sides):\n";
        auto strip_normalizer = normalizers::StripNormalizer::builder()
            .left(true)
            .right(true)
            .build();
        
        for (const auto& text : test_strings) {
            std::string normalized = strip_normalizer.normalize_str(text);
            std::cout << "\"" << text << "\" -> \"" << normalized << "\"\n";
        }
        std::cout << "\n";
        
        // Test 2: BERT Normalizer (lowercase only)
        std::cout << "BERT Normalizer (lowercase only):\n";
        auto bert_lowercase = normalizers::BertNormalizer::builder()
            .clean_text(false)
            .handle_chinese_chars(false)
            .strip_accents(false)
            .lowercase(true)
            .build();
        
        for (const auto& text : test_strings) {
            std::string normalized = bert_lowercase.normalize_str(text);
            std::cout << "\"" << text << "\" -> \"" << normalized << "\"\n";
        }
        std::cout << "\n";
        
        // Test 3: BERT Normalizer (strip accents only)
        std::cout << "BERT Normalizer (strip accents only):\n";
        auto bert_accents = normalizers::BertNormalizer::builder()
            .clean_text(false)
            .handle_chinese_chars(false)
            .strip_accents(true)
            .lowercase(false)
            .build();
        
        for (const auto& text : test_strings) {
            std::string normalized = bert_accents.normalize_str(text);
            std::cout << "\"" << text << "\" -> \"" << normalized << "\"\n";
        }
        std::cout << "\n";
        
        // Test 4: Full BERT Normalizer
        std::cout << "Full BERT Normalizer:\n";
        auto bert_full = normalizers::BertNormalizer::builder()
            .clean_text(true)
            .handle_chinese_chars(true)
            .strip_accents(true)
            .lowercase(true)
            .build();
        
        for (const auto& text : test_strings) {
            std::string normalized = bert_full.normalize_str(text);
            std::cout << "\"" << text << "\" -> \"" << normalized << "\"\n";
        }
        std::cout << "\n";
        
        std::cout << "Example 2: Sequence Normalizer\n";
        std::cout << "------------------------------\n\n";
        
        // Create a sequence normalizer that combines multiple steps
        auto sequence_normalizer = normalizers::SequenceNormalizer::builder()
            .add(std::make_unique<normalizers::StripNormalizer>(true, true))
            .add(std::make_unique<normalizers::BertNormalizer>(true, false, true, true))
            .build();
        
        std::cout << "Sequence: Strip -> BERT (clean + strip accents + lowercase):\n";
        for (const auto& text : test_strings) {
            std::string normalized = sequence_normalizer.normalize_str(text);
            std::cout << "\"" << text << "\" -> \"" << normalized << "\"\n";
        }
        std::cout << "\n";
        
        std::cout << "Example 3: Tokenizer with Normalizer\n";
        std::cout << "------------------------------------\n\n";
        
        // Create tokenizer with BPE model
        auto bpe_model = std::make_unique<models::BPE>(vocab, merges);
        Tokenizer tokenizer(std::move(bpe_model));
        
        // Set a normalizer on the tokenizer
        auto tokenizer_normalizer = std::make_unique<normalizers::BertNormalizer>(true, false, true, true);
        tokenizer.set_normalizer(std::move(tokenizer_normalizer));
        
        std::cout << "Tokenizer with BERT normalizer:\n";
        for (const auto& text : test_strings) {
            auto encoding = tokenizer.encode(text);
            
            const auto& tokens = encoding.get_tokens();
            const auto& ids = encoding.get_ids();
            
            std::cout << "Input: \"" << text << "\"\n";
            std::cout << "Tokens: [";
            for (size_t i = 0; i < tokens.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << tokens[i] << "\"";
            }
            std::cout << "]\n";
            
            std::cout << "IDs: [";
            for (size_t i = 0; i < ids.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << ids[i];
            }
            std::cout << "]\n\n";
        }
        
        std::cout << "Example 4: Comparing Different Normalizers\n";
        std::cout << "------------------------------------------\n\n";
        
        std::string test_text = "  CAFÉ naïve RÉSUMÉ  ";
        std::cout << "Original text: \"" << test_text << "\"\n\n";
        
        // No normalization
        auto no_norm_model = std::make_unique<models::BPE>(vocab, merges);
        Tokenizer no_norm_tokenizer(std::move(no_norm_model));
        auto no_norm_encoding = no_norm_tokenizer.encode(test_text);
        std::cout << "No normalization: " << no_norm_encoding.get_tokens().size() << " tokens\n";
        
        // Strip only
        auto strip_model = std::make_unique<models::BPE>(vocab, merges);
        Tokenizer strip_tokenizer(std::move(strip_model));
        strip_tokenizer.set_normalizer(std::make_unique<normalizers::StripNormalizer>());
        auto strip_encoding = strip_tokenizer.encode(test_text);
        std::cout << "Strip only: " << strip_encoding.get_tokens().size() << " tokens\n";
        
        // Full BERT normalization
        auto bert_model = std::make_unique<models::BPE>(vocab, merges);
        Tokenizer bert_tokenizer(std::move(bert_model));
        bert_tokenizer.set_normalizer(std::make_unique<normalizers::BertNormalizer>());
        auto bert_encoding = bert_tokenizer.encode(test_text);
        std::cout << "Full BERT: " << bert_encoding.get_tokens().size() << " tokens\n";
        
        std::cout << "\nNormalizers example completed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
