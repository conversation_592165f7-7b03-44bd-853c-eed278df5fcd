#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/trainers/bpe_trainer.hpp"

using namespace tokenizers;

int main() {
    std::cout << "Tokenizers C++ Training Example\n";
    std::cout << "===============================\n\n";
    
    try {
        // Create training data
        std::vector<std::string> training_data = {
            "hello world this is a test",
            "hello there how are you doing",
            "world peace is important for everyone",
            "this is another test sentence",
            "hello world again and again",
            "testing the tokenizer training process",
            "world class performance is our goal",
            "hello to all the wonderful people",
            "this test should help us learn",
            "world wide web is amazing",
            "hello everyone welcome to the show",
            "testing testing one two three",
            "world of possibilities awaits us",
            "hello beautiful world of tomorrow",
            "this is the final test sentence"
        };
        
        std::cout << "Training data contains " << training_data.size() << " sentences\n\n";
        
        // Create a BPE trainer
        auto trainer = trainers::BPETrainer::builder()
            .vocab_size(100)  // Small vocabulary for demonstration
            .min_frequency(2)
            .show_progress(true)
            .special_tokens({
                AddedToken::from("<unk>", true),
                AddedToken::from("<pad>", true),
                AddedToken::from("<s>", true),
                AddedToken::from("</s>", true)
            })
            .build();
        
        std::cout << "Created BPE trainer with:\n";
        std::cout << "- Target vocabulary size: " << trainer.get_vocab_size() << "\n";
        std::cout << "- Minimum frequency: " << trainer.get_min_frequency() << "\n";
        std::cout << "- Special tokens: " << trainer.get_special_tokens().size() << "\n\n";
        
        // Create an empty BPE model
        auto bpe_model = std::make_unique<models::BPE>();
        
        // Create tokenizer with the empty model
        Tokenizer tokenizer(std::move(bpe_model));
        
        std::cout << "Starting training...\n";
        std::cout << "===================\n";
        
        // Train the tokenizer
        tokenizer.train(trainer, training_data);
        
        std::cout << "\nTraining completed!\n\n";
        
        // Test the trained tokenizer
        std::cout << "Testing the trained tokenizer:\n";
        std::cout << "==============================\n";
        
        std::vector<std::string> test_sentences = {
            "hello world",
            "this is a test",
            "world peace",
            "testing the tokenizer",
            "hello beautiful world"
        };
        
        for (const auto& sentence : test_sentences) {
            std::cout << "Input: \"" << sentence << "\"\n";
            
            auto encoding = tokenizer.encode(sentence);
            
            const auto& tokens = encoding.get_tokens();
            const auto& ids = encoding.get_ids();
            
            std::cout << "Tokens: [";
            for (size_t i = 0; i < tokens.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << tokens[i] << "\"";
            }
            std::cout << "]\n";
            
            std::cout << "IDs: [";
            for (size_t i = 0; i < ids.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << ids[i];
            }
            std::cout << "]\n";
            
            // Test decoding
            auto decoded = tokenizer.decode(ids);
            std::cout << "Decoded: \"" << decoded << "\"\n";
            
            std::cout << "\n";
        }
        
        // Show vocabulary information
        std::cout << "Vocabulary Information:\n";
        std::cout << "======================\n";
        std::cout << "Final vocabulary size: " << tokenizer.get_vocab_size() << "\n";
        
        // Show some vocabulary entries
        auto vocab = tokenizer.get_vocab();
        std::cout << "Sample vocabulary entries:\n";
        
        int count = 0;
        for (const auto& [token, id] : vocab) {
            if (count >= 20) break;  // Show first 20 entries
            std::cout << "\"" << token << "\" -> " << id << "\n";
            count++;
        }
        
        if (vocab.size() > 20) {
            std::cout << "... and " << (vocab.size() - 20) << " more entries\n";
        }
        
        std::cout << "\n";
        
        // Test special tokens
        std::cout << "Special Tokens:\n";
        std::cout << "===============\n";
        
        std::vector<std::string> special_token_names = {"<unk>", "<pad>", "<s>", "</s>"};
        for (const auto& token_name : special_token_names) {
            auto id = tokenizer.token_to_id(token_name);
            if (id) {
                std::cout << "\"" << token_name << "\" -> ID " << *id << "\n";
            } else {
                std::cout << "\"" << token_name << "\" -> Not found\n";
            }
        }
        
        std::cout << "\n";
        
        // Save the trained tokenizer (placeholder - actual implementation would save to JSON)
        std::cout << "Saving trained tokenizer...\n";
        try {
            tokenizer.save("trained_tokenizer.json", true);
            std::cout << "Tokenizer saved to 'trained_tokenizer.json'\n";
        } catch (const std::exception& e) {
            std::cout << "Note: Saving not fully implemented yet (" << e.what() << ")\n";
        }
        
        std::cout << "\nTraining example completed successfully!\n";
        
        // Demonstrate the difference between trained and untrained tokenization
        std::cout << "\nComparison with character-level tokenization:\n";
        std::cout << "============================================\n";
        
        std::string test_text = "hello world";
        auto encoding = tokenizer.encode(test_text);
        
        std::cout << "Text: \"" << test_text << "\"\n";
        std::cout << "Trained tokenizer: " << encoding.size() << " tokens\n";
        std::cout << "Character-level would be: " << test_text.length() << " tokens\n";
        std::cout << "Compression ratio: " << (double)test_text.length() / encoding.size() << "x\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
