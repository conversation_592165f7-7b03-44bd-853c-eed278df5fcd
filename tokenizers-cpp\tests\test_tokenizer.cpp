#include <gtest/gtest.h>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/trainers/bpe_trainer.hpp"

using namespace tokenizers;

class TokenizerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a simple BPE model for testing
        models::BPE::Vocab vocab = {
            {"h", 0}, {"e", 1}, {"l", 2}, {"o", 3}, {" ", 4}, {"w", 5}, {"r", 6}, {"d", 7},
            {"he", 8}, {"ll", 9}, {"lo", 10}, {"wo", 11}, {"or", 12}, {"rl", 13}, {"ld", 14}
        };
        
        models::BPE::Merges merges = {
            {"h", "e"}, {"l", "l"}, {"l", "o"}, {"w", "o"}, {"o", "r"}, {"r", "l"}, {"l", "d"}
        };
        
        auto bpe_model = std::make_unique<models::BPE>(vocab, merges);
        tokenizer_ = std::make_unique<Tokenizer>(std::move(bpe_model));
    }

    std::unique_ptr<Tokenizer> tokenizer_;
};

TEST_F(TokenizerTest, BasicEncoding) {
    auto encoding = tokenizer_->encode("hello world");
    
    EXPECT_FALSE(encoding.is_empty());
    EXPECT_GT(encoding.size(), 0);
    
    const auto& tokens = encoding.get_tokens();
    const auto& ids = encoding.get_ids();
    
    EXPECT_EQ(tokens.size(), ids.size());
    
    // Check that we get some reasonable tokens
    bool found_hello_part = false;
    bool found_world_part = false;
    
    for (const auto& token : tokens) {
        if (token.find("h") != std::string::npos || token.find("e") != std::string::npos) {
            found_hello_part = true;
        }
        if (token.find("w") != std::string::npos || token.find("o") != std::string::npos) {
            found_world_part = true;
        }
    }
    
    EXPECT_TRUE(found_hello_part);
    EXPECT_TRUE(found_world_part);
}

TEST_F(TokenizerTest, EmptyInput) {
    auto encoding = tokenizer_->encode("");
    
    EXPECT_TRUE(encoding.is_empty());
    EXPECT_EQ(encoding.size(), 0);
}

TEST_F(TokenizerTest, TokenToId) {
    auto id = tokenizer_->token_to_id("h");
    EXPECT_TRUE(id.has_value());
    EXPECT_EQ(*id, 0);
    
    auto no_id = tokenizer_->token_to_id("xyz");
    EXPECT_FALSE(no_id.has_value());
}

TEST_F(TokenizerTest, IdToToken) {
    auto token = tokenizer_->id_to_token(0);
    EXPECT_TRUE(token.has_value());
    EXPECT_EQ(*token, "h");
    
    auto no_token = tokenizer_->id_to_token(999);
    EXPECT_FALSE(no_token.has_value());
}

TEST_F(TokenizerTest, VocabSize) {
    auto vocab_size = tokenizer_->get_vocab_size();
    EXPECT_GT(vocab_size, 0);
    EXPECT_EQ(vocab_size, 15);  // Our test vocab has 15 tokens
}

TEST_F(TokenizerTest, Decode) {
    std::vector<uint32_t> ids = {0, 1, 2, 2, 3};  // h, e, l, l, o
    auto decoded = tokenizer_->decode(ids);
    
    EXPECT_FALSE(decoded.empty());
    // The exact result depends on the decoder, but it should contain the characters
    EXPECT_NE(decoded.find("h"), std::string::npos);
    EXPECT_NE(decoded.find("e"), std::string::npos);
    EXPECT_NE(decoded.find("l"), std::string::npos);
    EXPECT_NE(decoded.find("o"), std::string::npos);
}

TEST_F(TokenizerTest, BatchEncoding) {
    std::vector<std::string> sequences = {"hello", "world", "test"};
    auto encodings = tokenizer_->encode_batch(sequences);
    
    EXPECT_EQ(encodings.size(), 3);
    
    for (const auto& encoding : encodings) {
        EXPECT_FALSE(encoding.is_empty());
        EXPECT_GT(encoding.size(), 0);
    }
}

TEST_F(TokenizerTest, BatchDecoding) {
    std::vector<std::vector<uint32_t>> sequences = {
        {0, 1},      // h, e
        {5, 3},      // w, o
        {2, 2}       // l, l
    };
    
    auto decoded = tokenizer_->decode_batch(sequences);
    
    EXPECT_EQ(decoded.size(), 3);
    
    for (const auto& text : decoded) {
        EXPECT_FALSE(text.empty());
    }
}

TEST_F(TokenizerTest, AddTokens) {
    std::vector<std::string> new_tokens = {"<unk>", "<pad>"};
    auto added_count = tokenizer_->add_tokens(new_tokens);
    
    EXPECT_EQ(added_count, 2);
    
    // Check that the tokens were added
    auto unk_id = tokenizer_->token_to_id("<unk>");
    auto pad_id = tokenizer_->token_to_id("<pad>");
    
    EXPECT_TRUE(unk_id.has_value());
    EXPECT_TRUE(pad_id.has_value());
}

TEST_F(TokenizerTest, AddSpecialTokens) {
    std::vector<AddedToken> special_tokens = {
        AddedToken::from("<s>", true),
        AddedToken::from("</s>", true)
    };
    
    auto added_count = tokenizer_->add_special_tokens(special_tokens);
    
    EXPECT_EQ(added_count, 2);
    
    // Check that the special tokens were added
    auto start_id = tokenizer_->token_to_id("<s>");
    auto end_id = tokenizer_->token_to_id("</s>");
    
    EXPECT_TRUE(start_id.has_value());
    EXPECT_TRUE(end_id.has_value());
}

// Test for BPE model specifically
class BPEModelTest : public ::testing::Test {
protected:
    void SetUp() override {
        vocab_ = {
            {"a", 0}, {"b", 1}, {"c", 2}, {"ab", 3}, {"bc", 4}, {"abc", 5}
        };
        
        merges_ = {
            {"a", "b"}, {"ab", "c"}
        };
        
        bpe_ = std::make_unique<models::BPE>(vocab_, merges_);
    }

    models::BPE::Vocab vocab_;
    models::BPE::Merges merges_;
    std::unique_ptr<models::BPE> bpe_;
};

TEST_F(BPEModelTest, BasicTokenization) {
    auto tokens = bpe_->tokenize("abc");
    
    EXPECT_FALSE(tokens.empty());
    
    // Check that we get reasonable tokens
    bool found_tokens = false;
    for (const auto& token : tokens) {
        if (!token.value.empty()) {
            found_tokens = true;
            break;
        }
    }
    EXPECT_TRUE(found_tokens);
}

TEST_F(BPEModelTest, VocabAccess) {
    auto vocab = bpe_->get_vocab();
    EXPECT_EQ(vocab.size(), 6);
    
    EXPECT_EQ(vocab["a"], 0);
    EXPECT_EQ(vocab["b"], 1);
    EXPECT_EQ(vocab["abc"], 5);
}

TEST_F(BPEModelTest, TokenIdMapping) {
    EXPECT_EQ(bpe_->token_to_id("a"), 0);
    EXPECT_EQ(bpe_->token_to_id("abc"), 5);
    EXPECT_FALSE(bpe_->token_to_id("xyz").has_value());
    
    EXPECT_EQ(bpe_->id_to_token(0), "a");
    EXPECT_EQ(bpe_->id_to_token(5), "abc");
    EXPECT_FALSE(bpe_->id_to_token(999).has_value());
}

TEST_F(BPEModelTest, Clone) {
    auto cloned = bpe_->clone();
    EXPECT_NE(cloned.get(), nullptr);
    
    // Check that the clone has the same vocabulary
    EXPECT_EQ(cloned->get_vocab_size(), bpe_->get_vocab_size());
    EXPECT_EQ(cloned->token_to_id("a"), bpe_->token_to_id("a"));
}

// Test for BPE trainer
TEST(BPETrainerTest, BasicTraining) {
    auto trainer = trainers::BPETrainer::builder()
        .vocab_size(100)
        .min_frequency(1)
        .show_progress(false)
        .build();
    
    EXPECT_EQ(trainer.get_vocab_size(), 100);
    EXPECT_EQ(trainer.get_min_frequency(), 1);
    EXPECT_FALSE(trainer.get_show_progress());
}

TEST(BPETrainerTest, SpecialTokens) {
    std::vector<AddedToken> special_tokens = {
        AddedToken::from("<unk>", true),
        AddedToken::from("<pad>", true)
    };
    
    auto trainer = trainers::BPETrainer::builder()
        .special_tokens(special_tokens)
        .build();
    
    const auto& tokens = trainer.get_special_tokens();
    EXPECT_EQ(tokens.size(), 2);
    EXPECT_EQ(tokens[0].content, "<unk>");
    EXPECT_EQ(tokens[1].content, "<pad>");
    EXPECT_TRUE(tokens[0].special);
    EXPECT_TRUE(tokens[1].special);
}
