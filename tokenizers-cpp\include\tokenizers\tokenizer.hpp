#pragma once

#include "encoding.hpp"
#include "added_vocabulary.hpp"
#include "models/model.hpp"
#include "normalizers/normalizer.hpp"
#include "pre_tokenizers/pre_tokenizer.hpp"
#include "processors/post_processor.hpp"
#include "decoders/decoder.hpp"
#include "trainers/trainer.hpp"
#include <memory>
#include <optional>
#include <string>
#include <vector>
#include <unordered_map>
#include <cstdint>

namespace tokenizers {

/**
 * @brief Parameters for truncation
 */
struct TruncationParams {
    /// Maximum length after truncation
    size_t max_length = 512;
    
    /// Stride for overflow when truncating
    size_t stride = 0;
    
    /// Truncation strategy ("longest_first", "only_first", "only_second")
    std::string strategy = "longest_first";
    
    /// Truncation direction ("left" or "right")
    std::string direction = "right";
};

/**
 * @brief Parameters for padding
 */
struct PaddingParams {
    /// Padding strategy ("longest", "max_length", "do_not_pad")
    std::string strategy = "longest";
    
    /// Padding direction ("left" or "right")
    std::string direction = "right";
    
    /// Padding token ID
    uint32_t pad_id = 0;
    
    /// Padding type ID
    uint32_t pad_type_id = 0;
    
    /// Padding token string
    std::string pad_token = "[PAD]";
    
    /// Maximum length for padding (when strategy is "max_length")
    std::optional<size_t> pad_to_multiple_of = std::nullopt;
};

/**
 * @brief Main Tokenizer class
 * 
 * A Tokenizer is capable of encoding/decoding any text. It is composed of several
 * components that work together in a pipeline:
 * 1. Normalizer: Normalizes the input text
 * 2. PreTokenizer: Splits the text into initial tokens
 * 3. Model: Applies the core tokenization algorithm
 * 4. PostProcessor: Adds special tokens and handles sequence pairs
 * 5. Decoder: Converts tokens back to readable text
 */
class Tokenizer {
private:
    /// The core tokenization model
    std::unique_ptr<models::Model> model_;
    
    /// Optional text normalizer
    std::unique_ptr<normalizers::Normalizer> normalizer_;
    
    /// Optional pre-tokenizer
    std::unique_ptr<pre_tokenizers::PreTokenizer> pre_tokenizer_;
    
    /// Optional post-processor
    std::unique_ptr<processors::PostProcessor> post_processor_;
    
    /// Optional decoder
    std::unique_ptr<decoders::Decoder> decoder_;
    
    /// Added vocabulary for special tokens
    AddedVocabulary added_vocabulary_;
    
    /// Truncation parameters
    std::optional<TruncationParams> truncation_;
    
    /// Padding parameters
    std::optional<PaddingParams> padding_;

public:
    /**
     * @brief Construct a new Tokenizer with a model
     * 
     * @param model The tokenization model to use
     */
    explicit Tokenizer(std::unique_ptr<models::Model> model);
    
    /**
     * @brief Destructor
     */
    ~Tokenizer() = default;
    
    /**
     * @brief Copy constructor (deleted - use clone instead)
     */
    Tokenizer(const Tokenizer&) = delete;
    
    /**
     * @brief Move constructor
     */
    Tokenizer(Tokenizer&&) = default;
    
    /**
     * @brief Copy assignment (deleted - use clone instead)
     */
    Tokenizer& operator=(const Tokenizer&) = delete;
    
    /**
     * @brief Move assignment
     */
    Tokenizer& operator=(Tokenizer&&) = default;
    
    // Configuration methods
    
    /**
     * @brief Set the normalizer
     * 
     * @param normalizer The normalizer to use
     */
    void set_normalizer(std::unique_ptr<normalizers::Normalizer> normalizer);
    
    /**
     * @brief Set the pre-tokenizer
     * 
     * @param pre_tokenizer The pre-tokenizer to use
     */
    void set_pre_tokenizer(std::unique_ptr<pre_tokenizers::PreTokenizer> pre_tokenizer);
    
    /**
     * @brief Set the post-processor
     * 
     * @param post_processor The post-processor to use
     */
    void set_post_processor(std::unique_ptr<processors::PostProcessor> post_processor);
    
    /**
     * @brief Set the decoder
     * 
     * @param decoder The decoder to use
     */
    void set_decoder(std::unique_ptr<decoders::Decoder> decoder);
    
    /**
     * @brief Set truncation parameters
     * 
     * @param truncation The truncation parameters
     */
    void set_truncation(const std::optional<TruncationParams>& truncation);
    
    /**
     * @brief Set padding parameters
     * 
     * @param padding The padding parameters
     */
    void set_padding(const std::optional<PaddingParams>& padding);
    
    // Core functionality
    
    /**
     * @brief Encode a single text sequence
     * 
     * @param sequence The input text to encode
     * @param pair Optional second sequence for sequence pairs
     * @param add_special_tokens Whether to add special tokens
     * @return Encoding The encoded result
     */
    Encoding encode(const std::string& sequence,
                   const std::optional<std::string>& pair = std::nullopt,
                   bool add_special_tokens = true);
    
    /**
     * @brief Encode multiple text sequences
     * 
     * @param sequences The input texts to encode
     * @param add_special_tokens Whether to add special tokens
     * @return std::vector<Encoding> The encoded results
     */
    std::vector<Encoding> encode_batch(const std::vector<std::string>& sequences,
                                     bool add_special_tokens = true);
    
    /**
     * @brief Decode token IDs back to text
     * 
     * @param ids The token IDs to decode
     * @param skip_special_tokens Whether to skip special tokens
     * @return std::string The decoded text
     */
    std::string decode(const std::vector<uint32_t>& ids, bool skip_special_tokens = true);
    
    /**
     * @brief Decode multiple sequences of token IDs
     * 
     * @param sequences The token ID sequences to decode
     * @param skip_special_tokens Whether to skip special tokens
     * @return std::vector<std::string> The decoded texts
     */
    std::vector<std::string> decode_batch(const std::vector<std::vector<uint32_t>>& sequences,
                                        bool skip_special_tokens = true);
    
    // Training
    
    /**
     * @brief Train the tokenizer from files
     * 
     * @param trainer The trainer to use
     * @param files The training files
     */
    void train_from_files(trainers::Trainer& trainer, const std::vector<std::string>& files);
    
    /**
     * @brief Train the tokenizer from iterator
     * 
     * @param trainer The trainer to use
     * @param sequences The training sequences
     */
    void train(trainers::Trainer& trainer, const std::vector<std::string>& sequences);
    
    // Serialization
    
    /**
     * @brief Save the tokenizer to a file
     * 
     * @param path The file path to save to
     * @param pretty Whether to format the JSON nicely
     */
    void save(const std::string& path, bool pretty = false) const;
    
    /**
     * @brief Load a tokenizer from a file
     * 
     * @param path The file path to load from
     * @return std::unique_ptr<Tokenizer> The loaded tokenizer
     */
    static std::unique_ptr<Tokenizer> from_file(const std::string& path);
    
    // Vocabulary management
    
    /**
     * @brief Add tokens to the vocabulary
     * 
     * @param tokens The tokens to add
     * @return size_t The number of tokens added
     */
    size_t add_tokens(const std::vector<std::string>& tokens);
    
    /**
     * @brief Add special tokens to the vocabulary
     * 
     * @param tokens The special tokens to add
     * @return size_t The number of tokens added
     */
    size_t add_special_tokens(const std::vector<AddedToken>& tokens);
    
    /**
     * @brief Get token ID for a string
     * 
     * @param token The token string
     * @return std::optional<uint32_t> The token ID if found
     */
    std::optional<uint32_t> token_to_id(const std::string& token) const;
    
    /**
     * @brief Get token string for an ID
     * 
     * @param id The token ID
     * @return std::optional<std::string> The token string if found
     */
    std::optional<std::string> id_to_token(uint32_t id) const;
    
    /**
     * @brief Get the vocabulary size
     * 
     * @param with_added_tokens Whether to include added tokens
     * @return size_t The vocabulary size
     */
    size_t get_vocab_size(bool with_added_tokens = true) const;
    
    /**
     * @brief Get the full vocabulary
     * 
     * @param with_added_tokens Whether to include added tokens
     * @return std::unordered_map<std::string, uint32_t> The vocabulary map
     */
    std::unordered_map<std::string, uint32_t> get_vocab(bool with_added_tokens = true) const;
    
    /**
     * @brief Clone this tokenizer
     * 
     * @return std::unique_ptr<Tokenizer> A copy of this tokenizer
     */
    std::unique_ptr<Tokenizer> clone() const;

private:
    /**
     * @brief Apply truncation to an encoding
     * 
     * @param encoding The encoding to truncate
     */
    void apply_truncation(Encoding& encoding) const;
    
    /**
     * @brief Apply padding to an encoding
     * 
     * @param encoding The encoding to pad
     */
    void apply_padding(Encoding& encoding) const;
    
    /**
     * @brief Apply padding to multiple encodings
     * 
     * @param encodings The encodings to pad
     */
    void apply_padding_batch(std::vector<Encoding>& encodings) const;
};

} // namespace tokenizers
