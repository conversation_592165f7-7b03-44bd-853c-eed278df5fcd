#pragma once

#include "pre_tokenizer.hpp"
#include <string>
#include <vector>
#include <memory>

namespace tokenizers {

namespace pre_tokenizers {

/**
 * @brief Whitespace pre-tokenizer that splits text on whitespace characters
 * 
 * This pre-tokenizer splits the input text on whitespace boundaries, creating
 * separate tokens for each word. It's one of the most basic pre-tokenization
 * strategies.
 */
class Whitespace : public PreTokenizer {
public:
    /**
     * @brief Default constructor
     */
    Whitespace() = default;
    
    // PreTokenizer interface implementation
    void pre_tokenize(PreTokenizedString& pretokenized) override;
    std::unique_ptr<PreTokenizer> clone() const override;
    std::string get_type() const override { return "Whitespace"; }

private:
    /**
     * @brief Check if a character is whitespace
     * 
     * @param c The character to check
     * @return bool True if the character is whitespace
     */
    bool is_whitespace(char c) const;
    
    /**
     * @brief Split text on whitespace boundaries
     * 
     * @param text The text to split
     * @return std::vector<std::string> The split tokens
     */
    std::vector<std::string> split_on_whitespace(const std::string& text) const;
};

} // namespace pre_tokenizers
} // namespace tokenizers
