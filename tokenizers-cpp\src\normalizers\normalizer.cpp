#include "tokenizers/normalizers/normalizer.hpp"

namespace tokenizers {

// Forward declaration - we'll implement this properly later
class NormalizedString {
public:
    explicit NormalizedString(const std::string& text) : text_(text) {}
    const std::string& get() const { return text_; }
    void set(const std::string& text) { text_ = text; }
    
private:
    std::string text_;
};

namespace normalizers {

std::string Normalizer::normalize_str(const std::string& input) {
    NormalizedString normalized(input);
    normalize(normalized);
    return normalized.get();
}

} // namespace normalizers
} // namespace tokenizers
