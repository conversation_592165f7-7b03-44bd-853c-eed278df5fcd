#include "tokenizers/added_vocabulary.hpp"
#include <algorithm>
#include <regex>

namespace tokenizers {

// Forward declarations for classes we'll implement later
class NormalizedString {
public:
    // Placeholder implementation
    std::string get() const { return text_; }
    void set(const std::string& text) { text_ = text; }
    size_t len() const { return text_.length(); }
    
private:
    std::string text_;
};

class PreTokenizedString {
public:
    // Placeholder implementation
    std::vector<std::string> get_splits() const { return splits_; }
    void set_splits(const std::vector<std::string>& splits) { splits_ = splits; }
    
private:
    std::vector<std::string> splits_;
};

// Simple pattern matcher implementation
struct AddedVocabulary::PatternMatcher {
    std::vector<std::pair<std::regex, uint32_t>> patterns;
    
    void clear() {
        patterns.clear();
    }
    
    void add_pattern(const std::string& pattern, uint32_t token_id) {
        try {
            patterns.emplace_back(std::regex(pattern), token_id);
        } catch (const std::regex_error&) {
            // If regex compilation fails, create a simple literal pattern
            std::string escaped_pattern;
            for (char c : pattern) {
                if (std::string(".*+?^${}()|[]\\").find(c) != std::string::npos) {
                    escaped_pattern += '\\';
                }
                escaped_pattern += c;
            }
            patterns.emplace_back(std::regex(escaped_pattern), token_id);
        }
    }
    
    std::vector<std::pair<size_t, std::pair<size_t, uint32_t>>> find_matches(const std::string& text) const {
        std::vector<std::pair<size_t, std::pair<size_t, uint32_t>>> matches;
        
        for (const auto& [pattern, token_id] : patterns) {
            std::sregex_iterator iter(text.begin(), text.end(), pattern);
            std::sregex_iterator end;
            
            for (; iter != end; ++iter) {
                const std::smatch& match = *iter;
                matches.emplace_back(match.position(), std::make_pair(match.length(), token_id));
            }
        }
        
        // Sort matches by position
        std::sort(matches.begin(), matches.end());
        return matches;
    }
};

AddedVocabulary::AddedVocabulary() 
    : next_id_(0)
    , matcher_(std::make_unique<PatternMatcher>())
    , matcher_dirty_(false) {
}

AddedVocabulary::~AddedVocabulary() = default;

AddedVocabulary::AddedVocabulary(const AddedVocabulary& other)
    : added_tokens_map_(other.added_tokens_map_)
    , added_tokens_map_r_(other.added_tokens_map_r_)
    , special_tokens_(other.special_tokens_)
    , next_id_(other.next_id_)
    , matcher_(std::make_unique<PatternMatcher>())
    , matcher_dirty_(true) {
}

AddedVocabulary::AddedVocabulary(AddedVocabulary&& other) noexcept
    : added_tokens_map_(std::move(other.added_tokens_map_))
    , added_tokens_map_r_(std::move(other.added_tokens_map_r_))
    , special_tokens_(std::move(other.special_tokens_))
    , next_id_(other.next_id_)
    , matcher_(std::move(other.matcher_))
    , matcher_dirty_(other.matcher_dirty_) {
}

AddedVocabulary& AddedVocabulary::operator=(const AddedVocabulary& other) {
    if (this != &other) {
        added_tokens_map_ = other.added_tokens_map_;
        added_tokens_map_r_ = other.added_tokens_map_r_;
        special_tokens_ = other.special_tokens_;
        next_id_ = other.next_id_;
        matcher_ = std::make_unique<PatternMatcher>();
        matcher_dirty_ = true;
    }
    return *this;
}

AddedVocabulary& AddedVocabulary::operator=(AddedVocabulary&& other) noexcept {
    if (this != &other) {
        added_tokens_map_ = std::move(other.added_tokens_map_);
        added_tokens_map_r_ = std::move(other.added_tokens_map_r_);
        special_tokens_ = std::move(other.special_tokens_);
        next_id_ = other.next_id_;
        matcher_ = std::move(other.matcher_);
        matcher_dirty_ = other.matcher_dirty_;
    }
    return *this;
}

uint32_t AddedVocabulary::add_token(const AddedToken& token) {
    auto it = added_tokens_map_.find(token.content);
    if (it != added_tokens_map_.end()) {
        // Token already exists, update it
        uint32_t id = it->second;
        added_tokens_map_r_[id] = token;
        if (token.special) {
            special_tokens_.insert(id);
        } else {
            special_tokens_.erase(id);
        }
        mark_matcher_dirty();
        return id;
    }
    
    // Add new token
    uint32_t id = next_id_++;
    added_tokens_map_[token.content] = id;
    added_tokens_map_r_[id] = token;
    
    if (token.special) {
        special_tokens_.insert(id);
    }
    
    mark_matcher_dirty();
    return id;
}

std::vector<uint32_t> AddedVocabulary::add_tokens(const std::vector<AddedToken>& tokens) {
    std::vector<uint32_t> ids;
    ids.reserve(tokens.size());
    
    for (const auto& token : tokens) {
        ids.push_back(add_token(token));
    }
    
    return ids;
}

uint32_t AddedVocabulary::add_token(const std::string& token, bool special) {
    return add_token(AddedToken::from(token, special));
}

std::vector<uint32_t> AddedVocabulary::add_tokens(const std::vector<std::string>& tokens, bool special) {
    std::vector<uint32_t> ids;
    ids.reserve(tokens.size());
    
    for (const auto& token : tokens) {
        ids.push_back(add_token(token, special));
    }
    
    return ids;
}

std::optional<uint32_t> AddedVocabulary::token_to_id(const std::string& token) const {
    auto it = added_tokens_map_.find(token);
    if (it != added_tokens_map_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::optional<std::string> AddedVocabulary::id_to_token(uint32_t id) const {
    auto it = added_tokens_map_r_.find(id);
    if (it != added_tokens_map_r_.end()) {
        return it->second.content;
    }
    return std::nullopt;
}

std::optional<AddedToken> AddedVocabulary::get_added_token(uint32_t id) const {
    auto it = added_tokens_map_r_.find(id);
    if (it != added_tokens_map_r_.end()) {
        return it->second;
    }
    return std::nullopt;
}

bool AddedVocabulary::is_special_token(uint32_t id) const {
    return special_tokens_.find(id) != special_tokens_.end();
}

void AddedVocabulary::clear() {
    added_tokens_map_.clear();
    added_tokens_map_r_.clear();
    special_tokens_.clear();
    next_id_ = 0;
    matcher_->clear();
    matcher_dirty_ = false;
}

std::vector<Token> AddedVocabulary::extract_and_normalize(NormalizedString& normalized) const {
    std::vector<Token> tokens;
    
    if (added_tokens_map_.empty()) {
        return tokens;
    }
    
    rebuild_matcher();
    
    std::string text = normalized.get();
    auto matches = matcher_->find_matches(text);
    
    // Convert matches to tokens
    for (const auto& [pos, length_and_id] : matches) {
        const auto& [length, token_id] = length_and_id;
        std::string token_text = text.substr(pos, length);
        tokens.emplace_back(token_id, std::move(token_text), std::make_pair(pos, pos + length));
    }
    
    return tokens;
}

void AddedVocabulary::split_pretokenized(PreTokenizedString& pretokenized) const {
    if (added_tokens_map_.empty()) {
        return;
    }
    
    rebuild_matcher();
    
    auto splits = pretokenized.get_splits();
    std::vector<std::string> new_splits;
    
    for (const auto& split : splits) {
        auto matches = matcher_->find_matches(split);
        
        if (matches.empty()) {
            new_splits.push_back(split);
            continue;
        }
        
        size_t last_end = 0;
        for (const auto& [pos, length_and_id] : matches) {
            const auto& [length, token_id] = length_and_id;
            
            // Add text before the match
            if (pos > last_end) {
                new_splits.push_back(split.substr(last_end, pos - last_end));
            }
            
            // Add the matched token
            new_splits.push_back(split.substr(pos, length));
            
            last_end = pos + length;
        }
        
        // Add remaining text
        if (last_end < split.length()) {
            new_splits.push_back(split.substr(last_end));
        }
    }
    
    pretokenized.set_splits(new_splits);
}

void AddedVocabulary::rebuild_matcher() const {
    if (!matcher_dirty_) {
        return;
    }

    matcher_->clear();

    for (const auto& [content, id] : added_tokens_map_) {
        matcher_->add_pattern(content, id);
    }

    // Use const_cast to modify mutable member in const method
    const_cast<AddedVocabulary*>(this)->matcher_dirty_ = false;
}

} // namespace tokenizers
