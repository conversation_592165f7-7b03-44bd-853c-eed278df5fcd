#pragma once

#include <string>
#include <memory>

namespace tokenizers {

// Forward declaration
class NormalizedString;

namespace normalizers {

/**
 * @brief Base class for all text normalizers
 * 
 * A Normalizer takes care of pre-processing strings. Common examples of normalization are
 * the unicode normalization standards, such as NFD or NFKC, case conversion, etc.
 */
class Normalizer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~Normalizer() = default;
    
    /**
     * @brief Normalize a NormalizedString in-place
     * 
     * This method allows modifying a NormalizedString to keep track of the alignment
     * information. If you just want to see the result of the normalization on a raw
     * string, you can use normalize_str.
     * 
     * @param normalized The normalized string to modify
     */
    virtual void normalize(NormalizedString& normalized) = 0;
    
    /**
     * @brief Normalize a raw string and return the result
     * 
     * This is a convenience method that creates a temporary NormalizedString,
     * applies normalization, and returns the result.
     * 
     * @param input The input string to normalize
     * @return std::string The normalized string
     */
    virtual std::string normalize_str(const std::string& input);
    
    /**
     * @brief Clone this normalizer
     * 
     * @return std::unique_ptr<Normalizer> A copy of this normalizer
     */
    virtual std::unique_ptr<Normalizer> clone() const = 0;
    
    /**
     * @brief Get the normalizer type name
     * 
     * @return std::string The normalizer type
     */
    virtual std::string get_type() const = 0;
};

} // namespace normalizers
} // namespace tokenizers
