#include <gtest/gtest.h>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/unigram.hpp"
#include "tokenizers/added_token.hpp"
#include <cmath>

using namespace tokenizers;

class UnigramTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a test vocabulary with scores
        test_vocab_ = {
            {"<unk>", {0, -10.0}},
            {"a", {1, -1.0}},
            {"b", {2, -2.0}},
            {"c", {3, -3.0}},
            {"ab", {4, -0.5}},
            {"bc", {5, -1.5}},
            {"abc", {6, -0.3}},
            {"hello", {7, -0.1}},
            {"world", {8, -0.2}},
            {"test", {9, -0.4}},
            {"ing", {10, -0.6}},
            {"ed", {11, -0.8}},
            {"the", {12, -0.05}},
            {"and", {13, -0.07}},
            {"is", {14, -0.09}},
            {"to", {15, -0.11}},
            {"of", {16, -0.13}},
            {"in", {17, -0.15}},
            {"that", {18, -0.17}},
            {"have", {19, -0.19}},
            {"it", {20, -0.21}},
            {"for", {21, -0.23}},
            {"not", {22, -0.25}},
            {"with", {23, -0.27}},
            {"he", {24, -0.29}},
            {"as", {25, -0.31}},
            {"you", {26, -0.33}},
            {"do", {27, -0.35}},
            {"at", {28, -0.37}}
        };
    }
    
    void TearDown() override {
        // Cleanup if needed
    }
    
    models::Unigram::Vocab test_vocab_;
    
    Tokenizer create_test_unigram_tokenizer() {
        auto model = std::make_unique<models::Unigram>(test_vocab_, "<unk>", -10.0);
        return Tokenizer(std::move(model));
    }
};

TEST_F(UnigramTest, BasicTokenization) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    // Test simple tokenization
    auto encoding = tokenizer.encode("hello");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_EQ(tokens.size(), 1);
    EXPECT_EQ(tokens[0], "hello");
    
    const auto& ids = encoding.get_ids();
    EXPECT_EQ(ids.size(), 1);
    EXPECT_EQ(ids[0], 7);  // ID for "hello"
}

TEST_F(UnigramTest, MultipleTokens) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    auto encoding = tokenizer.encode("hello world");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_GE(tokens.size(), 2);  // Should have at least 2 tokens
    
    // Check that we get reasonable tokens
    bool found_hello = false, found_world = false;
    for (const auto& token : tokens) {
        if (token == "hello") found_hello = true;
        if (token == "world") found_world = true;
    }
    
    EXPECT_TRUE(found_hello || found_world);  // Should find at least one
}

TEST_F(UnigramTest, UnknownTokenHandling) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    // Test with unknown characters
    auto encoding = tokenizer.encode("xyz");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_FALSE(tokens.empty());
    
    // Should handle unknown tokens gracefully
    for (const auto& token : tokens) {
        EXPECT_FALSE(token.empty());
    }
}

TEST_F(UnigramTest, VocabularyAccess) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    // Test token to ID mapping
    auto hello_id = tokenizer.token_to_id("hello");
    ASSERT_TRUE(hello_id.has_value());
    EXPECT_EQ(*hello_id, 7);
    
    auto world_id = tokenizer.token_to_id("world");
    ASSERT_TRUE(world_id.has_value());
    EXPECT_EQ(*world_id, 8);
    
    auto unk_id = tokenizer.token_to_id("<unk>");
    ASSERT_TRUE(unk_id.has_value());
    EXPECT_EQ(*unk_id, 0);
    
    // Test ID to token mapping
    auto token_7 = tokenizer.id_to_token(7);
    ASSERT_TRUE(token_7.has_value());
    EXPECT_EQ(*token_7, "hello");
    
    auto token_8 = tokenizer.id_to_token(8);
    ASSERT_TRUE(token_8.has_value());
    EXPECT_EQ(*token_8, "world");
}

TEST_F(UnigramTest, VocabularySize) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    size_t vocab_size = tokenizer.get_vocab_size();
    EXPECT_EQ(vocab_size, test_vocab_.size());
    
    auto vocab_map = tokenizer.get_vocab();
    EXPECT_EQ(vocab_map.size(), test_vocab_.size());
}

TEST_F(UnigramTest, EmptyInput) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    auto encoding = tokenizer.encode("");
    EXPECT_TRUE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_TRUE(tokens.empty());
    
    const auto& ids = encoding.get_ids();
    EXPECT_TRUE(ids.empty());
}

TEST_F(UnigramTest, SingleCharacter) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    auto encoding = tokenizer.encode("a");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_EQ(tokens.size(), 1);
    EXPECT_EQ(tokens[0], "a");
    
    const auto& ids = encoding.get_ids();
    EXPECT_EQ(ids.size(), 1);
    EXPECT_EQ(ids[0], 1);  // ID for "a"
}

TEST_F(UnigramTest, SubwordSegmentation) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    // Test that longer sequences get segmented appropriately
    auto encoding = tokenizer.encode("abc");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_FALSE(tokens.empty());
    
    // Should prefer longer matches when they have better scores
    // "abc" has score -0.3, which is better than "a" + "b" + "c" (-1.0 + -2.0 + -3.0 = -6.0)
    bool found_abc = false;
    for (const auto& token : tokens) {
        if (token == "abc") {
            found_abc = true;
            break;
        }
    }
    
    // Note: The exact segmentation depends on the Viterbi algorithm implementation
    // We just check that we get reasonable results
    EXPECT_FALSE(tokens.empty());
}

TEST_F(UnigramTest, OffsetTracking) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    std::string input = "hello world";
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    const auto& tokens = encoding.get_tokens();
    
    EXPECT_EQ(offsets.size(), tokens.size());
    
    // Check that offsets are valid
    for (const auto& offset : offsets) {
        EXPECT_LE(offset.first, input.length());
        EXPECT_LE(offset.second, input.length());
        EXPECT_LE(offset.first, offset.second);
    }
}

TEST_F(UnigramTest, ModelCloning) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    // Test that we can get the model type
    // Note: This requires access to the model, which might not be directly available
    // In a full implementation, we might need additional methods
    
    auto vocab_size = tokenizer.get_vocab_size();
    EXPECT_GT(vocab_size, 0);
    
    // Test that tokenization is consistent
    auto encoding1 = tokenizer.encode("hello world");
    auto encoding2 = tokenizer.encode("hello world");
    
    EXPECT_EQ(encoding1.get_tokens(), encoding2.get_tokens());
    EXPECT_EQ(encoding1.get_ids(), encoding2.get_ids());
}

TEST_F(UnigramTest, SpecialTokenHandling) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    // Add some special tokens
    std::vector<AddedToken> special_tokens = {
        AddedToken::from("[CLS]", true),
        AddedToken::from("[SEP]", true),
        AddedToken::from("[MASK]", true)
    };
    
    auto added_count = tokenizer.add_special_tokens(special_tokens);
    EXPECT_EQ(added_count, 3);
    
    // Test that special tokens are recognized
    EXPECT_TRUE(tokenizer.token_to_id("[CLS]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[SEP]").has_value());
    EXPECT_TRUE(tokenizer.token_to_id("[MASK]").has_value());
    
    // Test tokenization with special tokens
    auto encoding = tokenizer.encode("hello [MASK] world");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    bool found_mask = false;
    for (const auto& token : tokens) {
        if (token == "[MASK]") {
            found_mask = true;
            break;
        }
    }
    EXPECT_TRUE(found_mask);
}

TEST_F(UnigramTest, BatchProcessing) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    std::vector<std::string> batch_inputs = {
        "hello",
        "world",
        "test",
        "hello world"
    };
    
    auto batch_encodings = tokenizer.encode_batch(batch_inputs);
    EXPECT_EQ(batch_encodings.size(), batch_inputs.size());
    
    for (size_t i = 0; i < batch_encodings.size(); ++i) {
        EXPECT_FALSE(batch_encodings[i].is_empty()) 
            << "Empty encoding for input: " << batch_inputs[i];
    }
}

TEST_F(UnigramTest, ScoreBasedSegmentation) {
    // Create a vocabulary where different segmentations have different scores
    models::Unigram::Vocab score_test_vocab = {
        {"<unk>", {0, -10.0}},
        {"test", {1, -0.1}},    // Good score for "test"
        {"t", {2, -2.0}},       // Poor score for "t"
        {"e", {3, -2.0}},       // Poor score for "e"
        {"s", {4, -2.0}},       // Poor score for "s"
        {"te", {5, -1.0}},      // Medium score for "te"
        {"st", {6, -1.0}}       // Medium score for "st"
    };
    
    auto model = std::make_unique<models::Unigram>(score_test_vocab, "<unk>", -10.0);
    Tokenizer tokenizer(std::move(model));
    
    auto encoding = tokenizer.encode("test");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    
    // Should prefer "test" (score -0.1) over "t"+"e"+"s"+"t" (score -8.0)
    // The exact result depends on the Viterbi implementation
    EXPECT_FALSE(tokens.empty());
    
    // At minimum, we should get reasonable tokenization
    for (const auto& token : tokens) {
        EXPECT_FALSE(token.empty());
    }
}

TEST_F(UnigramTest, MinimumScoreFiltering) {
    auto tokenizer = create_test_unigram_tokenizer();
    
    // Test with a token that should be filtered by minimum score
    // All our test tokens have scores better than -10.0, so they should all be usable
    auto encoding = tokenizer.encode("the and is");
    EXPECT_FALSE(encoding.is_empty());
    
    const auto& tokens = encoding.get_tokens();
    EXPECT_FALSE(tokens.empty());
    
    // Should get reasonable segmentation
    for (const auto& token : tokens) {
        EXPECT_FALSE(token.empty());
    }
}
