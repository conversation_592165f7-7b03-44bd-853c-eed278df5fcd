# Trainers

<tokenizerslangcontent>
<python>
## BpeTrainer

[[autodoc]] tokenizers.trainers.BpeTrainer

## UnigramTrainer

[[autodoc]] tokenizers.trainers.UnigramTrainer

## WordLevelTrainer

[[autodoc]] tokenizers.trainers.WordLevelTrainer

## WordPieceTrainer

[[autodoc]] tokenizers.trainers.WordPieceTrainer
</python>
<rust>
The Rust API Reference is available directly on the [Docs.rs](https://docs.rs/tokenizers/latest/tokenizers/) website.
</rust>
<node>
The node API has not been documented yet.
</node>
</tokenizerslangcontent>