#include <gtest/gtest.h>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/models/wordpiece.hpp"
#include "tokenizers/normalizers/bert_normalizer.hpp"
#include "tokenizers/pre_tokenizers/bert_pre_tokenizer.hpp"
#include "tokenizers/decoders/wordpiece_decoder.hpp"

using namespace tokenizers;

// Helper macro to check offsets (similar to Rust version)
#define CHECK_OFFSETS(input, output, offset_idx, expected) \
    do { \
        const auto& offsets = output.get_offsets(); \
        ASSERT_LT(offset_idx, offsets.size()) << "Offset index out of range"; \
        auto offset_pair = offsets[offset_idx]; \
        std::string actual = input.substr(offset_pair.first, offset_pair.second - offset_pair.first); \
        EXPECT_EQ(actual, expected) << "Offset mismatch at index " << offset_idx; \
    } while(0)

class OffsetsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup code if needed
    }
    
    void TearDown() override {
        // Cleanup code if needed
    }
    
    // Helper function to create a basic BPE tokenizer
    Tokenizer create_basic_bpe_tokenizer() {
        // Create a simple BPE model for testing
        models::BPE::Vocab vocab = {
            {"h", 0}, {"e", 1}, {"l", 2}, {"o", 3}, {" ", 4}, {"w", 5}, {"r", 6}, {"d", 7},
            {"t", 8}, {"s", 9}, {"i", 10}, {"n", 11}, {"g", 12}, {"a", 13}, {"m", 14}, {"p", 15},
            {"x", 16}, {"c", 17}, {"u", 18}, {"f", 19}, {"y", 20}, {"b", 21}, {"k", 22}, {"j", 23},
            {",", 24}, {"?", 25}, {"!", 26}, {".", 27}, {"'", 28}, {"\"", 29}, {":", 30}, {";", 31},
            {"he", 32}, {"ll", 33}, {"lo", 34}, {"wo", 35}, {"or", 36}, {"rl", 37}, {"ld", 38},
            {"th", 39}, {"in", 40}, {"ng", 41}, {"an", 42}, {"am", 43}, {"te", 44}, {"st", 45},
            {"er", 46}, {"re", 47}, {"ar", 48}, {"ou", 49}, {"ow", 50}, {"ho", 51}, {"ow", 52}
        };
        
        models::BPE::Merges merges = {
            {"h", "e"}, {"l", "l"}, {"l", "o"}, {"w", "o"}, {"o", "r"}, {"r", "l"}, {"l", "d"},
            {"t", "h"}, {"i", "n"}, {"n", "g"}, {"a", "n"}, {"a", "m"}, {"t", "e"}, {"s", "t"},
            {"e", "r"}, {"r", "e"}, {"a", "r"}, {"o", "u"}, {"o", "w"}, {"h", "o"}
        };
        
        auto model = std::make_unique<models::BPE>(vocab, merges);
        return Tokenizer(std::move(model));
    }
    
    // Helper function to create a WordPiece tokenizer
    Tokenizer create_wordpiece_tokenizer() {
        models::WordPiece::Vocab vocab = {
            {"[UNK]", 0}, {"[CLS]", 1}, {"[SEP]", 2}, {"[PAD]", 3}, {"[MASK]", 4},
            {"h", 5}, {"e", 6}, {"l", 7}, {"o", 8}, {" ", 9}, {"w", 10}, {"r", 11}, {"d", 12},
            {"t", 13}, {"s", 14}, {"i", 15}, {"n", 16}, {"g", 17}, {"a", 18}, {"m", 19}, {"p", 20},
            {",", 21}, {"?", 22}, {"!", 23}, {".", 24}, {"'", 25}, {"\"", 26}, {":", 27}, {";", 28},
            {"##e", 29}, {"##l", 30}, {"##o", 31}, {"##r", 32}, {"##d", 33}, {"##s", 34}, {"##t", 35},
            {"##ing", 36}, {"##ed", 37}, {"##er", 38}, {"##ly", 39}, {"##tion", 40}
        };
        
        auto model = std::make_unique<models::WordPiece>(vocab, "[UNK]", "##", 100);
        return Tokenizer(std::move(model));
    }
};

TEST_F(OffsetsTest, BasicOffsets) {
    auto tokenizer = create_basic_bpe_tokenizer();
    
    std::string input = "hello world";
    auto encoding = tokenizer.encode(input);
    
    // Check that we have offsets
    const auto& offsets = encoding.get_offsets();
    EXPECT_FALSE(offsets.empty());
    
    // Check that offsets are within bounds
    for (const auto& offset : offsets) {
        EXPECT_LE(offset.first, input.length());
        EXPECT_LE(offset.second, input.length());
        EXPECT_LE(offset.first, offset.second);
    }
    
    // Check that offsets cover the entire input
    if (!offsets.empty()) {
        EXPECT_EQ(offsets.front().first, 0);
        EXPECT_EQ(offsets.back().second, input.length());
    }
}

TEST_F(OffsetsTest, WordPieceOffsets) {
    auto tokenizer = create_wordpiece_tokenizer();
    
    // Add BERT-style processing
    tokenizer.set_normalizer(std::make_unique<normalizers::BertNormalizer>());
    tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::BertPreTokenizer>());
    
    std::string input = "hello, world!";
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    const auto& tokens = encoding.get_tokens();
    
    EXPECT_EQ(offsets.size(), tokens.size());
    
    // Verify that each offset corresponds to the correct token
    for (size_t i = 0; i < offsets.size(); ++i) {
        auto offset_pair = offsets[i];
        if (offset_pair.first < offset_pair.second) {
            std::string text_segment = input.substr(offset_pair.first, 
                                                   offset_pair.second - offset_pair.first);
            // For WordPiece, the token might have ## prefix, so we check the core content
            std::string token = tokens[i];
            if (token.length() >= 2 && token.substr(0, 2) == "##") {
                token = token.substr(2);
            }
            
            // The text segment should contain the token content (after normalization)
            // This is a simplified check - in practice, normalization might change the text
            EXPECT_FALSE(text_segment.empty()) << "Empty text segment at index " << i;
        }
    }
}

TEST_F(OffsetsTest, SpecialTokenOffsets) {
    auto tokenizer = create_basic_bpe_tokenizer();
    
    // Add special tokens
    std::vector<AddedToken> special_tokens = {
        AddedToken::from("[CLS]", true),
        AddedToken::from("[SEP]", true)
    };
    tokenizer.add_special_tokens(special_tokens);
    
    std::string input = "hello [CLS] world [SEP]";
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    const auto& tokens = encoding.get_tokens();
    const auto& special_tokens_mask = encoding.get_special_tokens_mask();
    
    EXPECT_EQ(offsets.size(), tokens.size());
    EXPECT_EQ(offsets.size(), special_tokens_mask.size());
    
    // Check that special tokens have appropriate offsets
    for (size_t i = 0; i < tokens.size(); ++i) {
        if (special_tokens_mask[i] == 1) {
            // This is a special token
            auto offset_pair = offsets[i];
            if (offset_pair.first < offset_pair.second) {
                std::string text_segment = input.substr(offset_pair.first, 
                                                       offset_pair.second - offset_pair.first);
                EXPECT_EQ(text_segment, tokens[i]) << "Special token offset mismatch";
            }
        }
    }
}

TEST_F(OffsetsTest, EmptyInput) {
    auto tokenizer = create_basic_bpe_tokenizer();
    
    std::string input = "";
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    EXPECT_TRUE(offsets.empty());
}

TEST_F(OffsetsTest, SingleCharacter) {
    auto tokenizer = create_basic_bpe_tokenizer();
    
    std::string input = "a";
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    EXPECT_FALSE(offsets.empty());
    
    if (!offsets.empty()) {
        EXPECT_EQ(offsets[0].first, 0);
        EXPECT_EQ(offsets[0].second, 1);
    }
}

TEST_F(OffsetsTest, UnicodeCharacters) {
    auto tokenizer = create_basic_bpe_tokenizer();
    
    // Test with Unicode characters
    std::string input = "café";
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    EXPECT_FALSE(offsets.empty());
    
    // Check that offsets are valid for Unicode string
    for (const auto& offset : offsets) {
        EXPECT_LE(offset.first, input.length());
        EXPECT_LE(offset.second, input.length());
        EXPECT_LE(offset.first, offset.second);
    }
}

TEST_F(OffsetsTest, ConsecutiveSpaces) {
    auto tokenizer = create_basic_bpe_tokenizer();
    
    std::string input = "hello    world";  // Multiple spaces
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    const auto& tokens = encoding.get_tokens();
    
    EXPECT_EQ(offsets.size(), tokens.size());
    
    // Verify that offsets are contiguous or properly spaced
    for (size_t i = 0; i < offsets.size(); ++i) {
        auto offset_pair = offsets[i];
        EXPECT_LE(offset_pair.first, offset_pair.second);
        
        if (i > 0) {
            // Current offset should start at or after the previous one ends
            EXPECT_GE(offset_pair.first, offsets[i-1].first);
        }
    }
}

TEST_F(OffsetsTest, PunctuationHandling) {
    auto tokenizer = create_wordpiece_tokenizer();
    tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::BertPreTokenizer>());
    
    std::string input = "Hello, world! How are you?";
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    const auto& tokens = encoding.get_tokens();
    
    EXPECT_EQ(offsets.size(), tokens.size());
    
    // Check that punctuation tokens have correct offsets
    for (size_t i = 0; i < tokens.size(); ++i) {
        const std::string& token = tokens[i];
        auto offset_pair = offsets[i];
        
        if (token == "," || token == "!" || token == "?") {
            // Punctuation should have single-character offsets
            if (offset_pair.first < offset_pair.second) {
                EXPECT_EQ(offset_pair.second - offset_pair.first, 1);
                std::string text_segment = input.substr(offset_pair.first, 1);
                EXPECT_EQ(text_segment, token);
            }
        }
    }
}

TEST_F(OffsetsTest, OffsetConsistency) {
    auto tokenizer = create_basic_bpe_tokenizer();
    
    std::string input = "The quick brown fox jumps over the lazy dog.";
    auto encoding = tokenizer.encode(input);
    
    const auto& offsets = encoding.get_offsets();
    const auto& tokens = encoding.get_tokens();
    
    EXPECT_EQ(offsets.size(), tokens.size());
    
    // Reconstruct the text using offsets and verify it matches
    std::string reconstructed;
    size_t last_end = 0;
    
    for (size_t i = 0; i < offsets.size(); ++i) {
        auto offset_pair = offsets[i];
        
        // Add any gap between tokens
        if (offset_pair.first > last_end) {
            reconstructed += input.substr(last_end, offset_pair.first - last_end);
        }
        
        // Add the token text
        if (offset_pair.first < offset_pair.second) {
            reconstructed += input.substr(offset_pair.first, offset_pair.second - offset_pair.first);
        }
        
        last_end = offset_pair.second;
    }
    
    // Add any remaining text
    if (last_end < input.length()) {
        reconstructed += input.substr(last_end);
    }
    
    EXPECT_EQ(reconstructed, input) << "Reconstructed text doesn't match original";
}
