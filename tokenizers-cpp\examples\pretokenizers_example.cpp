#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include "tokenizers/tokenizer.hpp"
#include "tokenizers/models/bpe.hpp"
#include "tokenizers/pre_tokenizers/whitespace.hpp"
#include "tokenizers/pre_tokenizers/bert_pre_tokenizer.hpp"

using namespace tokenizers;

int main() {
    std::cout << "PreTokenizers Example\n";
    std::cout << "====================\n\n";
    
    try {
        // Test strings with various pre-tokenization challenges
        std::vector<std::string> test_strings = {
            "Hello world!",                    // Basic case
            "Don't split contractions",       // Contractions
            "Split on punctuation: yes, no?", // Multiple punctuation
            "<EMAIL> works",         // Email addresses
            "Numbers 123.45 and dates",       // Numbers
            "Hyphenated-words here",          // Hyphens
            "Multiple   spaces   here",       // Multiple spaces
            "Mixed,punctuation;here!",        // Dense punctuation
            "CamelCaseWords",                 // CamelCase
            "under_score_words"               // Underscores
        };
        
        std::cout << "Example 1: Whitespace PreTokenizer\n";
        std::cout << "----------------------------------\n\n";
        
        auto whitespace_pretokenizer = std::make_unique<pre_tokenizers::Whitespace>();
        
        for (const auto& text : test_strings) {
            auto splits = whitespace_pretokenizer->pre_tokenize_str(text);
            
            std::cout << "Input: \"" << text << "\"\n";
            std::cout << "Splits: [";
            for (size_t i = 0; i < splits.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << splits[i] << "\"";
            }
            std::cout << "] (" << splits.size() << " tokens)\n\n";
        }
        
        std::cout << "Example 2: BERT PreTokenizer\n";
        std::cout << "----------------------------\n\n";
        
        auto bert_pretokenizer = std::make_unique<pre_tokenizers::BertPreTokenizer>();
        
        for (const auto& text : test_strings) {
            auto splits = bert_pretokenizer->pre_tokenize_str(text);
            
            std::cout << "Input: \"" << text << "\"\n";
            std::cout << "Splits: [";
            for (size_t i = 0; i < splits.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << splits[i] << "\"";
            }
            std::cout << "] (" << splits.size() << " tokens)\n\n";
        }
        
        std::cout << "Example 3: Comparison of PreTokenizers\n";
        std::cout << "--------------------------------------\n\n";
        
        std::string comparison_text = "Hello, world! Don't split this.";
        std::cout << "Comparison text: \"" << comparison_text << "\"\n\n";
        
        // Whitespace
        auto ws_splits = whitespace_pretokenizer->pre_tokenize_str(comparison_text);
        std::cout << "Whitespace (" << ws_splits.size() << " tokens): [";
        for (size_t i = 0; i < ws_splits.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << "\"" << ws_splits[i] << "\"";
        }
        std::cout << "]\n\n";
        
        // BERT
        auto bert_splits = bert_pretokenizer->pre_tokenize_str(comparison_text);
        std::cout << "BERT (" << bert_splits.size() << " tokens): [";
        for (size_t i = 0; i < bert_splits.size(); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << "\"" << bert_splits[i] << "\"";
        }
        std::cout << "]\n\n";
        
        std::cout << "Example 4: Tokenizer with PreTokenizer\n";
        std::cout << "--------------------------------------\n\n";
        
        // Create a simple BPE model
        models::BPE::Vocab vocab = {
            {"h", 0}, {"e", 1}, {"l", 2}, {"o", 3}, {" ", 4}, {"w", 5}, {"r", 6}, {"d", 7},
            {"!", 8}, {"t", 9}, {"s", 10}, {"i", 11}, {"n", 12}, {"'", 13}, {"p", 14}, {"a", 15},
            {"c", 16}, {"m", 17}, {".", 18}, {"y", 19}, {"u", 20}, {",", 21}, {"?", 22}, {"b", 23},
            {"he", 24}, {"ll", 25}, {"lo", 26}, {"wo", 27}, {"or", 28}, {"rl", 29}, {"ld", 30},
            {"th", 31}, {"in", 32}, {"ng", 33}, {"an", 34}, {"is", 35}, {"it", 36}, {"on", 37}
        };
        
        models::BPE::Merges merges = {
            {"h", "e"}, {"l", "l"}, {"l", "o"}, {"w", "o"}, {"o", "r"}, {"r", "l"}, {"l", "d"},
            {"t", "h"}, {"i", "n"}, {"n", "g"}, {"a", "n"}, {"i", "s"}, {"i", "t"}, {"o", "n"}
        };
        
        // Test with different pre-tokenizers
        std::vector<std::string> tokenizer_test_strings = {
            "Hello, world!",
            "Don't split this.",
            "Test punctuation: yes!"
        };
        
        for (const auto& test_text : tokenizer_test_strings) {
            std::cout << "Input: \"" << test_text << "\"\n";
            
            // No pre-tokenizer
            auto no_pretok_model = std::make_unique<models::BPE>(vocab, merges);
            Tokenizer no_pretok_tokenizer(std::move(no_pretok_model));
            auto no_pretok_encoding = no_pretok_tokenizer.encode(test_text);
            std::cout << "No PreTokenizer: " << no_pretok_encoding.get_tokens().size() << " tokens\n";
            
            // Whitespace pre-tokenizer
            auto ws_model = std::make_unique<models::BPE>(vocab, merges);
            Tokenizer ws_tokenizer(std::move(ws_model));
            ws_tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::Whitespace>());
            auto ws_encoding = ws_tokenizer.encode(test_text);
            std::cout << "Whitespace PreTokenizer: " << ws_encoding.get_tokens().size() << " tokens -> [";
            const auto& ws_tokens = ws_encoding.get_tokens();
            for (size_t i = 0; i < ws_tokens.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << ws_tokens[i] << "\"";
            }
            std::cout << "]\n";
            
            // BERT pre-tokenizer
            auto bert_model = std::make_unique<models::BPE>(vocab, merges);
            Tokenizer bert_tokenizer(std::move(bert_model));
            bert_tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::BertPreTokenizer>());
            auto bert_encoding = bert_tokenizer.encode(test_text);
            std::cout << "BERT PreTokenizer: " << bert_encoding.get_tokens().size() << " tokens -> [";
            const auto& bert_tokens = bert_encoding.get_tokens();
            for (size_t i = 0; i < bert_tokens.size(); ++i) {
                if (i > 0) std::cout << ", ";
                std::cout << "\"" << bert_tokens[i] << "\"";
            }
            std::cout << "]\n\n";
        }
        
        std::cout << "Example 5: Impact on Tokenization Quality\n";
        std::cout << "----------------------------------------\n\n";
        
        std::string quality_test = "The quick-brown fox jumps, doesn't it?";
        std::cout << "Quality test: \"" << quality_test << "\"\n\n";
        
        // Compare tokenization results
        auto quality_vocab = vocab;
        auto quality_merges = merges;
        
        // Add some relevant tokens
        quality_vocab["quick"] = 100;
        quality_vocab["brown"] = 101;
        quality_vocab["fox"] = 102;
        quality_vocab["jumps"] = 103;
        quality_vocab["doesn"] = 104;
        quality_vocab["-"] = 105;
        
        // Test different approaches
        std::cout << "Tokenization comparison:\n";
        
        // Whitespace approach
        auto ws_qual_model = std::make_unique<models::BPE>(quality_vocab, quality_merges);
        Tokenizer ws_qual_tokenizer(std::move(ws_qual_model));
        ws_qual_tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::Whitespace>());
        auto ws_qual_encoding = ws_qual_tokenizer.encode(quality_test);
        std::cout << "Whitespace: " << ws_qual_encoding.get_tokens().size() << " tokens\n";
        
        // BERT approach
        auto bert_qual_model = std::make_unique<models::BPE>(quality_vocab, quality_merges);
        Tokenizer bert_qual_tokenizer(std::move(bert_qual_model));
        bert_qual_tokenizer.set_pre_tokenizer(std::make_unique<pre_tokenizers::BertPreTokenizer>());
        auto bert_qual_encoding = bert_qual_tokenizer.encode(quality_test);
        std::cout << "BERT: " << bert_qual_encoding.get_tokens().size() << " tokens\n";
        
        std::cout << "\nPreTokenizers example completed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
