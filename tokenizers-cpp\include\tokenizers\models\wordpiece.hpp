#pragma once

#include "model.hpp"
#include "../trainers/trainer.hpp"
#include <unordered_map>
#include <string>
#include <vector>
#include <optional>
#include <cstdint>

namespace tokenizers {
namespace models {

/**
 * @brief A WordPiece model
 * 
 * WordPiece is a subword tokenization algorithm that uses a greedy longest-match-first
 * algorithm to split words into subword units. It's commonly used in BERT and other
 * transformer models.
 */
class WordPiece : public Model {
public:
    /// Type alias for vocabulary (token -> ID mapping)
    using Vocab = std::unordered_map<std::string, uint32_t>;
    
    /// Type alias for reverse vocabulary (ID -> token mapping)
    using VocabR = std::unordered_map<uint32_t, std::string>;

private:
    /// The vocabulary assigns a number to each token
    Vocab vocab_;
    
    /// Reversed vocabulary, to rebuild sentences
    VocabR vocab_r_;
    
    /// The unknown token to be used when we encounter an unknown character
    std::string unk_token_;
    
    /// The prefix to be used for continuing subwords
    std::string continuing_subword_prefix_;
    
    /// Maximum number of characters per word
    size_t max_input_chars_per_word_;

public:
    /**
     * @brief Default constructor
     */
    WordPiece();
    
    /**
     * @brief Construct WordPiece with vocabulary
     * 
     * @param vocab The vocabulary mapping
     * @param unk_token The unknown token
     * @param continuing_subword_prefix The prefix for continuing subwords (default: "##")
     * @param max_input_chars_per_word Maximum characters per word (default: 100)
     */
    WordPiece(Vocab vocab,
              const std::string& unk_token = "[UNK]",
              const std::string& continuing_subword_prefix = "##",
              size_t max_input_chars_per_word = 100);
    
    /**
     * @brief Create WordPiece from vocabulary file
     * 
     * @param vocab_file Path to vocabulary file (text format, one token per line)
     * @param unk_token The unknown token
     * @param continuing_subword_prefix The prefix for continuing subwords
     * @param max_input_chars_per_word Maximum characters per word
     * @return WordPiece The constructed WordPiece model
     */
    static WordPiece from_file(const std::string& vocab_file,
                              const std::string& unk_token = "[UNK]",
                              const std::string& continuing_subword_prefix = "##",
                              size_t max_input_chars_per_word = 100);
    
    /**
     * @brief Builder class for WordPiece
     */
    class Builder {
    private:
        std::optional<Vocab> vocab_;
        std::string unk_token_ = "[UNK]";
        std::string continuing_subword_prefix_ = "##";
        size_t max_input_chars_per_word_ = 100;
        
    public:
        Builder() = default;
        
        Builder& vocab(const Vocab& vocab) { vocab_ = vocab; return *this; }
        Builder& unk_token(const std::string& unk_token) { unk_token_ = unk_token; return *this; }
        Builder& continuing_subword_prefix(const std::string& prefix) { continuing_subword_prefix_ = prefix; return *this; }
        Builder& max_input_chars_per_word(size_t max_chars) { max_input_chars_per_word_ = max_chars; return *this; }
        
        WordPiece build();
    };
    
    /**
     * @brief Create a builder for WordPiece
     * 
     * @return Builder A WordPiece builder instance
     */
    static Builder builder() { return Builder(); }
    
    // Model interface implementation
    std::vector<Token> tokenize(const std::string& sequence) override;
    std::optional<uint32_t> token_to_id(const std::string& token) const override;
    std::optional<std::string> id_to_token(uint32_t id) const override;
    std::unordered_map<std::string, uint32_t> get_vocab() const override;
    size_t get_vocab_size() const override;
    std::unique_ptr<trainers::Trainer> get_trainer() override;
    void save(const std::string& path, bool pretty = false) const override;
    std::unique_ptr<Model> clone() const override;
    std::string get_type() const override { return "WordPiece"; }
    
    // WordPiece-specific methods
    
    /**
     * @brief Get the unknown token
     * 
     * @return const std::string& The unknown token
     */
    const std::string& get_unk_token() const { return unk_token_; }
    
    /**
     * @brief Set the unknown token
     * 
     * @param unk_token The unknown token
     */
    void set_unk_token(const std::string& unk_token) { unk_token_ = unk_token; }
    
    /**
     * @brief Get the continuing subword prefix
     * 
     * @return const std::string& The continuing subword prefix
     */
    const std::string& get_continuing_subword_prefix() const { return continuing_subword_prefix_; }
    
    /**
     * @brief Set the continuing subword prefix
     * 
     * @param prefix The continuing subword prefix
     */
    void set_continuing_subword_prefix(const std::string& prefix) { continuing_subword_prefix_ = prefix; }
    
    /**
     * @brief Get the maximum input characters per word
     * 
     * @return size_t The maximum input characters per word
     */
    size_t get_max_input_chars_per_word() const { return max_input_chars_per_word_; }
    
    /**
     * @brief Set the maximum input characters per word
     * 
     * @param max_chars The maximum input characters per word
     */
    void set_max_input_chars_per_word(size_t max_chars) { max_input_chars_per_word_ = max_chars; }

private:
    /**
     * @brief Apply WordPiece tokenization to a single word
     * 
     * @param word The word to tokenize
     * @return std::vector<std::string> The subword tokens
     */
    std::vector<std::string> tokenize_word(const std::string& word) const;
    
    /**
     * @brief Convert a word to tokens with proper IDs and offsets
     * 
     * @param word The original word
     * @param subwords The subword tokens
     * @param start_offset Starting character offset
     * @return std::vector<Token> The tokens with IDs and offsets
     */
    std::vector<Token> word_to_tokens(const std::string& word,
                                    const std::vector<std::string>& subwords,
                                    size_t start_offset) const;
};

} // namespace models
} // namespace tokenizers
